2025-06-16 01:23:33,385 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 01:23:33,386 - root - INFO - Author: <PERSON><PERSON><PERSON><PERSON>
2025-06-16 01:23:33,386 - root - INFO - Company: Agevolami SRL
2025-06-16 01:23:33,386 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 01:23:33,390 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\new_app\assets
2025-06-16 01:23:33,393 - flet - INFO - Starting up TCP server on localhost:11915
2025-06-16 01:23:33,405 - flet - INFO - Flet app has started...
2025-06-16 01:23:33,409 - flet - INFO - App URL: tcp://localhost:11915
2025-06-16 01:23:33,409 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 01:23:33,411 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:23:33,412 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:23:34,231 - flet - INFO - App session started
2025-06-16 01:24:33,467 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:24:33,467 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 01:24:38,468 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snack_bar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snack_bar(
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snack_bar'
2025-06-16 01:27:49,472 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 01:27:49,473 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 01:27:49,473 - root - INFO - Company: Agevolami SRL
2025-06-16 01:27:49,473 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 01:27:49,474 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\new_app\assets
2025-06-16 01:27:49,476 - flet - INFO - Starting up TCP server on localhost:12120
2025-06-16 01:27:49,485 - flet - INFO - Flet app has started...
2025-06-16 01:27:49,487 - flet - INFO - App URL: tcp://localhost:12120
2025-06-16 01:27:49,487 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 01:27:49,489 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:27:49,489 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:27:49,714 - flet - INFO - App session started
2025-06-16 01:27:55,199 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:27:55,199 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 01:27:57,174 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:11,189 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:12,194 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:12,987 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:13,782 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:14,503 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:15,479 - new_app.app.app_controller - INFO - Navigated to tab: location_comparison
2025-06-16 01:28:29,760 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:32,766 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:34,508 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:35,765 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:28:36,920 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AttributeError("'Page' object has no attribute 'show_snackbar'")>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 225, in navigate_to_tab
    self.show_error("Please complete the required steps before accessing this tab")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 564, in show_error
    self.page.show_snackbar(
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Page' object has no attribute 'show_snackbar'
2025-06-16 01:31:07,430 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 01:31:07,430 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 01:31:07,430 - root - INFO - Company: Agevolami SRL
2025-06-16 01:31:07,430 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 01:31:07,432 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\new_app\assets
2025-06-16 01:31:07,437 - flet - INFO - Starting up TCP server on localhost:12281
2025-06-16 01:31:07,445 - flet - INFO - Flet app has started...
2025-06-16 01:31:07,447 - flet - INFO - App URL: tcp://localhost:12281
2025-06-16 01:31:07,448 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 01:31:07,449 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:31:07,450 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:31:07,725 - flet - INFO - App session started
2025-06-16 01:31:15,567 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:31:15,568 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 01:31:25,115 - new_app.app.app_controller - INFO - Navigated to tab: location_comparison
2025-06-16 01:31:38,829 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:31:42,185 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:42,652 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:43,048 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:43,470 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:43,791 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:44,078 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:44,252 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:44,433 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:44,591 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:44,694 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:44,835 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:45,028 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:45,229 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:45,460 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:45,585 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:48,105 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:48,349 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:48,435 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:48,691 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:48,968 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:49,291 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:49,424 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:49,638 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:49,712 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:49,858 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:50,030 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:50,179 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:50,564 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:50,714 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:55,043 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:55,245 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:55,573 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:55,850 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:56,525 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:56,852 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:57,353 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:57,469 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:57,641 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:59,048 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:59,418 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:31:59,839 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:03,391 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:03,596 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:05,302 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:05,677 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:06,119 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:06,314 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:06,538 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:07,054 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:07,235 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:07,337 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:07,554 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:14,064 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:14,265 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:32:20,455 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:32:20,727 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:32:20,999 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:32:26,866 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:32:27,025 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:32:36,309 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:32:36,599 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:32:42,776 - new_app.app.app_controller - INFO - Action requested: save_configuration
2025-06-16 01:32:44,602 - new_app.app.app_controller - INFO - Action requested: run_comprehensive_analysis
2025-06-16 01:32:44,602 - new_app.app.app_controller - ERROR - Error handling action run_comprehensive_analysis: no running event loop
2025-06-16 01:36:10,718 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 01:36:10,718 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 01:36:10,719 - root - INFO - Company: Agevolami SRL
2025-06-16 01:36:10,719 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 01:36:10,722 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\new_app\assets
2025-06-16 01:36:10,729 - flet - INFO - Starting up TCP server on localhost:12581
2025-06-16 01:36:10,744 - flet - INFO - Flet app has started...
2025-06-16 01:36:10,749 - flet - INFO - App URL: tcp://localhost:12581
2025-06-16 01:36:10,749 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 01:36:10,752 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:36:10,753 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:36:11,099 - flet - INFO - App session started
2025-06-16 01:36:18,874 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:36:18,875 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 01:36:22,195 - new_app.app.app_controller - INFO - Navigated to tab: location_comparison
2025-06-16 01:36:23,746 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:36:26,053 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:26,346 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:26,465 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:26,679 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:26,901 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:27,205 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:27,412 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:27,603 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:27,679 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:27,815 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:28,329 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:28,782 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:29,022 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:29,227 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:29,688 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:29,829 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:30,183 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:33,170 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:34,056 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:35,500 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:37,030 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:37,192 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:37,317 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:37,467 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:37,620 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:37,797 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:38,281 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:38,544 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:38,707 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:38,877 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:39,030 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:39,126 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:39,315 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:39,525 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:39,849 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:42,830 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:42,992 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:43,414 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:43,666 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:44,146 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:44,984 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:45,359 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:45,918 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:46,164 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:46,796 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:50,898 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:51,032 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:36:53,575 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:36:53,792 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:36:54,050 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:36:56,952 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:36:59,149 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:37:01,123 - new_app.app.app_controller - INFO - Action requested: save_configuration
2025-06-16 01:37:04,636 - new_app.app.app_controller - INFO - Action requested: run_comprehensive_analysis
2025-06-16 01:37:04,690 - new_app.utils.file_utils - INFO - Created output directory structure: outputs\2025-06-16\Demo_Company_III
2025-06-16 01:37:04,716 - new_app.services.financial_service - ERROR - Error running financial model: EnhancedAssumptions.__init__() got an unexpected keyword argument 'project_life_years'
2025-06-16 01:37:04,719 - new_app.services.report_service - ERROR - Error generating comprehensive report: EnhancedAssumptions.__init__() got an unexpected keyword argument 'project_life_years'
2025-06-16 01:37:13,246 - new_app.app.app_controller - INFO - Action requested: run_financial_model
2025-06-16 01:37:13,270 - new_app.services.financial_service - ERROR - Error running financial model: EnhancedAssumptions.__init__() got an unexpected keyword argument 'project_life_years'
2025-06-16 01:37:15,812 - new_app.app.app_controller - INFO - Action requested: run_comprehensive_analysis
2025-06-16 01:37:15,830 - new_app.utils.file_utils - INFO - Created output directory structure: outputs\2025-06-16\Demo_Company_III
2025-06-16 01:37:15,841 - new_app.services.financial_service - ERROR - Error running financial model: EnhancedAssumptions.__init__() got an unexpected keyword argument 'project_life_years'
2025-06-16 01:37:15,841 - new_app.services.report_service - ERROR - Error generating comprehensive report: EnhancedAssumptions.__init__() got an unexpected keyword argument 'project_life_years'
2025-06-16 01:41:02,115 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 01:41:02,124 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 01:41:02,124 - root - INFO - Company: Agevolami SRL
2025-06-16 01:41:02,125 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 01:41:02,131 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\new_app\assets
2025-06-16 01:41:02,192 - flet - INFO - Starting up TCP server on localhost:12827
2025-06-16 01:41:02,355 - flet - INFO - Flet app has started...
2025-06-16 01:41:02,373 - flet - INFO - App URL: tcp://localhost:12827
2025-06-16 01:41:02,378 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 01:41:02,382 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:41:02,384 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:41:03,493 - flet - INFO - App session started
2025-06-16 01:41:11,100 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:41:11,114 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 01:41:14,234 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:14,776 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:16,111 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:16,328 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:16,538 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:16,896 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:17,011 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:17,288 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:17,427 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:17,581 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:17,722 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:17,806 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:18,157 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:20,977 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:21,244 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:21,461 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:21,659 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:21,766 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:21,890 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:22,115 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:23,806 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:24,017 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:24,150 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:24,380 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:24,644 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:25,143 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:25,282 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:25,464 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:25,513 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:25,657 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:25,840 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:26,307 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:26,440 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:26,717 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:26,807 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:32,013 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:32,190 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:32,497 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:32,676 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:33,005 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:36,465 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:36,698 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:36,989 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:39,134 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:39,372 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:41:43,823 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:41:44,313 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:41:45,862 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:41:46,873 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:41:47,122 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:41:49,524 - new_app.app.app_controller - INFO - Action requested: run_comprehensive_analysis
2025-06-16 01:41:49,580 - new_app.utils.file_utils - INFO - Created output directory structure: outputs\2025-06-16\Demo_company_3
2025-06-16 01:41:50,532 - new_app.services.financial_service - INFO - Financial model executed successfully
2025-06-16 01:41:50,588 - new_app.services.validation_service - INFO - Starting comprehensive model validation
2025-06-16 01:41:50,599 - new_app.services.validation_service - INFO - Validation completed - Valid: False
2025-06-16 01:41:50,673 - new_app.services.validation_service - INFO - Generating benchmark comparison
2025-06-16 01:41:50,681 - new_app.services.validation_service - ERROR - Error generating benchmark comparison: argument of type 'EnhancedProjectAssumptions' is not iterable
2025-06-16 01:41:50,691 - new_app.services.report_service - ERROR - Error generating comprehensive report: argument of type 'EnhancedProjectAssumptions' is not iterable
2025-06-16 01:42:02,473 - new_app.app.app_controller - INFO - Action requested: run_financial_model
2025-06-16 01:42:02,688 - new_app.services.financial_service - INFO - Financial model executed successfully
2025-06-16 01:42:06,337 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AssertionError('value cannot be negative')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 238, in navigate_to_tab
    self.main_content.content = view.get_content()
                                ~~~~~~~~~~~~~~~~^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\views\base_view.py", line 39, in get_content
    self._content = self.build_content()
                    ~~~~~~~~~~~~~~~~~~^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\views\dashboard_view.py", line 44, in build_content
    kpi_summary = self._create_kpi_summary()
  File "D:\pro projects\flet\Hiel financial model\src\new_app\views\dashboard_view.py", line 82, in _create_kpi_summary
    ).build(),
      ~~~~~^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\components\widgets\kpi_card.py", line 55, in build
    progress_indicator = self._create_progress_indicator()
  File "D:\pro projects\flet\Hiel financial model\src\new_app\components\widgets\kpi_card.py", line 99, in _create_progress_indicator
    ft.ProgressBar(
    ~~~~~~~~~~~~~~^
        value=progress,
        ^^^^^^^^^^^^^^^
    ...<2 lines>...
        height=4
        ^^^^^^^^
    ),
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\progress_bar.py", line 134, in __init__
    self.value = value
    ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\progress_bar.py", line 160, in value
    assert value is None or value >= 0, "value cannot be negative"
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: value cannot be negative
2025-06-16 01:42:09,883 - new_app.app.app_controller - INFO - Navigated to tab: location_comparison
2025-06-16 01:42:10,851 - new_app.app.app_controller - INFO - Navigated to tab: financial_model
2025-06-16 01:42:14,515 - new_app.app.app_controller - INFO - Navigated to tab: validation
2025-06-16 01:42:20,071 - new_app.app.app_controller - INFO - Navigated to tab: sensitivity
2025-06-16 01:42:25,756 - new_app.app.app_controller - INFO - Action requested: run_sensitivity_analysis
2025-06-16 01:42:26,961 - new_app.services.financial_service - INFO - Sensitivity analysis completed successfully
2025-06-16 01:42:30,970 - new_app.app.app_controller - INFO - Navigated to tab: monte_carlo
2025-06-16 01:42:32,627 - new_app.app.app_controller - INFO - Action requested: run_monte_carlo
2025-06-16 01:42:59,129 - new_app.services.financial_service - INFO - Monte Carlo simulation completed with 1000 simulations
2025-06-16 01:42:59,137 - new_app.app.app_controller - INFO - Action requested: run_monte_carlo
2025-06-16 01:42:59,140 - new_app.app.app_controller - INFO - Navigated to tab: monte_carlo
2025-06-16 01:42:59,141 - new_app.app.app_controller - INFO - Navigated to tab: monte_carlo
2025-06-16 01:42:59,143 - new_app.app.app_controller - INFO - Navigated to tab: monte_carlo
2025-06-16 01:42:59,144 - new_app.app.app_controller - INFO - Navigated to tab: monte_carlo
2025-06-16 01:43:07,878 - new_app.services.financial_service - INFO - Monte Carlo simulation completed with 1000 simulations
2025-06-16 01:43:43,986 - new_app.app.app_controller - INFO - Navigated to tab: scenarios
2025-06-16 01:43:44,919 - new_app.app.app_controller - INFO - Navigated to tab: monte_carlo
2025-06-16 01:43:50,398 - new_app.app.app_controller - INFO - Navigated to tab: sensitivity
2025-06-16 01:43:51,312 - new_app.app.app_controller - INFO - Navigated to tab: validation
2025-06-16 01:43:52,567 - new_app.app.app_controller - INFO - Navigated to tab: financial_model
2025-06-16 01:43:53,981 - new_app.app.app_controller - INFO - Navigated to tab: location_comparison
2025-06-16 01:43:54,952 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=AssertionError('value cannot be negative')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\page.py", line 906, in wrapper
    handler(*args)
    ~~~~~~~^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 219, in _on_navigation_change
    self.navigate_to_tab(selected_tab)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\app\app_controller.py", line 238, in navigate_to_tab
    self.main_content.content = view.get_content()
                                ~~~~~~~~~~~~~~~~^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\views\base_view.py", line 39, in get_content
    self._content = self.build_content()
                    ~~~~~~~~~~~~~~~~~~^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\views\dashboard_view.py", line 44, in build_content
    kpi_summary = self._create_kpi_summary()
  File "D:\pro projects\flet\Hiel financial model\src\new_app\views\dashboard_view.py", line 82, in _create_kpi_summary
    ).build(),
      ~~~~~^^
  File "D:\pro projects\flet\Hiel financial model\src\new_app\components\widgets\kpi_card.py", line 55, in build
    progress_indicator = self._create_progress_indicator()
  File "D:\pro projects\flet\Hiel financial model\src\new_app\components\widgets\kpi_card.py", line 99, in _create_progress_indicator
    ft.ProgressBar(
    ~~~~~~~~~~~~~~^
        value=progress,
        ^^^^^^^^^^^^^^^
    ...<2 lines>...
        height=4
        ^^^^^^^^
    ),
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\progress_bar.py", line 134, in __init__
    self.value = value
    ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet\core\progress_bar.py", line 160, in value
    assert value is None or value >= 0, "value cannot be negative"
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: value cannot be negative
2025-06-16 01:43:56,640 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:46:00,052 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 01:46:00,052 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 01:46:00,052 - root - INFO - Company: Agevolami SRL
2025-06-16 01:46:00,052 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 01:46:00,054 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\new_app\assets
2025-06-16 01:46:00,057 - flet - INFO - Starting up TCP server on localhost:13030
2025-06-16 01:46:00,065 - flet - INFO - Flet app has started...
2025-06-16 01:46:00,068 - flet - INFO - App URL: tcp://localhost:13030
2025-06-16 01:46:00,068 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 01:46:00,069 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:46:00,070 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:46:00,386 - flet - INFO - App session started
2025-06-16 01:47:02,500 - root - INFO - Starting Enhanced Financial Model Application
2025-06-16 01:47:02,501 - root - INFO - Author: Abdelhalim Serhani
2025-06-16 01:47:02,501 - root - INFO - Company: Agevolami SRL
2025-06-16 01:47:02,501 - root - INFO - Website: www.agevolami.it & www.agevolami.ma
2025-06-16 01:47:02,502 - flet - INFO - Assets path configured: D:\pro projects\flet\Hiel financial model\src\new_app\assets
2025-06-16 01:47:02,505 - flet - INFO - Starting up TCP server on localhost:13067
2025-06-16 01:47:02,511 - flet - INFO - Flet app has started...
2025-06-16 01:47:02,513 - flet - INFO - App URL: tcp://localhost:13067
2025-06-16 01:47:02,513 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 01:47:02,514 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:47:02,515 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 01:47:02,686 - flet - INFO - App session started
2025-06-16 01:47:18,489 - new_app.app.app_controller - INFO - Navigated to tab: project_setup
2025-06-16 01:47:18,489 - root - INFO - Enhanced Financial Model Application started successfully
2025-06-16 01:48:01,013 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:48:01,310 - new_app.app.app_controller - INFO - Data changed: project_assumptions
2025-06-16 01:48:05,212 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:05,606 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:05,758 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:05,948 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:06,201 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:18,841 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:19,138 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:19,701 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:19,945 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:20,122 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:20,286 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:20,370 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:20,498 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:20,726 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:22,198 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:22,473 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:22,903 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:23,268 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:23,536 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:23,676 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:23,781 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:23,962 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:24,042 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:24,161 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:24,355 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:28,108 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:28,278 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:28,678 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:28,836 - new_app.app.app_controller - INFO - Data changed: client_profile
2025-06-16 01:48:33,209 - new_app.app.app_controller - INFO - Action requested: run_comprehensive_analysis
2025-06-16 01:48:33,221 - new_app.utils.file_utils - INFO - Created output directory structure: outputs\2025-06-16\Demo_Company
2025-06-16 01:48:33,248 - new_app.services.financial_service - INFO - Financial model executed successfully
2025-06-16 01:48:33,256 - new_app.services.validation_service - INFO - Starting comprehensive model validation
2025-06-16 01:48:33,257 - new_app.services.validation_service - INFO - Validation completed - Valid: False
2025-06-16 01:48:33,262 - new_app.services.validation_service - INFO - Generating benchmark comparison
2025-06-16 01:48:33,263 - new_app.services.validation_service - ERROR - Error generating benchmark comparison: argument of type 'EnhancedProjectAssumptions' is not iterable
2025-06-16 01:48:33,263 - new_app.services.report_service - ERROR - Error generating comprehensive report: argument of type 'EnhancedProjectAssumptions' is not iterable
