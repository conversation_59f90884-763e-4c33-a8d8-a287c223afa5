"""
Notification System
===================

Advanced notification and feedback system.
"""

import flet as ft
from typing import Optional, Callable
from enum import Enum
import asyncio
from datetime import datetime


class NotificationType(Enum):
    """Notification type enumeration."""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class NotificationSystem:
    """Advanced notification system."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.notifications: list = []
        self.container: Optional[ft.Column] = None
        self.max_notifications = 5
        self.auto_dismiss_delay = 5  # seconds
    
    def initialize(self):
        """Initialize the notification system."""
        self.container = ft.Column(
            controls=[],
            spacing=10,
            alignment=ft.MainAxisAlignment.END,
            horizontal_alignment=ft.CrossAxisAlignment.END
        )
        
        # Add to page overlay
        notification_overlay = ft.Container(
            content=self.container,
            right=20,
            top=80,
            width=350
        )
        
        self.page.overlay.append(notification_overlay)
    
    def show_notification(self, 
                         message: str,
                         notification_type: NotificationType = NotificationType.INFO,
                         title: str = "",
                         auto_dismiss: bool = True,
                         action_text: str = "",
                         action_callback: Optional[Callable] = None) -> str:
        """Show a notification."""
        
        notification_id = f"notification_{len(self.notifications)}_{datetime.now().timestamp()}"
        
        # Create notification
        notification = self._create_notification(
            notification_id,
            message,
            notification_type,
            title,
            action_text,
            action_callback
        )
        
        # Add to container
        if self.container:
            self.notifications.append(notification_id)
            self.container.controls.append(notification)
            
            # Remove oldest if exceeding max
            if len(self.notifications) > self.max_notifications:
                self._remove_oldest_notification()
            
            self.page.update()
            
            # Auto dismiss
            if auto_dismiss:
                asyncio.create_task(
                    self._auto_dismiss_notification(notification_id, self.auto_dismiss_delay)
                )
        
        return notification_id
    
    def _create_notification(self,
                           notification_id: str,
                           message: str,
                           notification_type: NotificationType,
                           title: str,
                           action_text: str,
                           action_callback: Optional[Callable]) -> ft.Container:
        """Create a notification widget."""
        
        # Get colors and icon based on type
        colors = self._get_notification_colors(notification_type)
        icon = self._get_notification_icon(notification_type)
        
        # Content components
        content_controls = []
        
        # Icon and title/message row
        main_row_controls = [
            ft.Icon(icon, color=colors['icon'], size=24)
        ]
        
        # Text content
        text_column = []
        if title:
            text_column.append(
                ft.Text(title, size=14, weight=ft.FontWeight.BOLD, color=colors['text'])
            )
        
        text_column.append(
            ft.Text(message, size=12, color=colors['text'])
        )
        
        main_row_controls.append(
            ft.Column(text_column, spacing=2, expand=True)
        )
        
        # Close button
        close_button = ft.IconButton(
            icon=ft.Icons.CLOSE,
            icon_size=16,
            on_click=lambda _: self.dismiss_notification(notification_id),
            tooltip="Dismiss"
        )
        main_row_controls.append(close_button)
        
        content_controls.append(
            ft.Row(main_row_controls, alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        )
        
        # Action button
        if action_text and action_callback:
            action_button = ft.TextButton(
                action_text,
                on_click=lambda _: self._handle_action(action_callback, notification_id),
                style=ft.ButtonStyle(color=colors['action'])
            )
            content_controls.append(
                ft.Row([action_button], alignment=ft.MainAxisAlignment.END)
            )
        
        # Create notification container
        notification = ft.Container(
            content=ft.Column(content_controls, spacing=5),
            padding=15,
            bgcolor=colors['background'],
            border=ft.border.all(1, colors['border']),
            border_radius=8,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=5,
                color=ft.Colors.GREY_400,
                offset=ft.Offset(0, 2)
            ),
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
            data=notification_id
        )
        
        return notification
    
    def _get_notification_colors(self, notification_type: NotificationType) -> dict:
        """Get colors for notification type."""
        color_schemes = {
            NotificationType.SUCCESS: {
                'background': ft.Colors.GREEN_50,
                'border': ft.Colors.GREEN_200,
                'icon': ft.Colors.GREEN_600,
                'text': ft.Colors.GREEN_800,
                'action': ft.Colors.GREEN_600
            },
            NotificationType.ERROR: {
                'background': ft.Colors.RED_50,
                'border': ft.Colors.RED_200,
                'icon': ft.Colors.RED_600,
                'text': ft.Colors.RED_800,
                'action': ft.Colors.RED_600
            },
            NotificationType.WARNING: {
                'background': ft.Colors.ORANGE_50,
                'border': ft.Colors.ORANGE_200,
                'icon': ft.Colors.ORANGE_600,
                'text': ft.Colors.ORANGE_800,
                'action': ft.Colors.ORANGE_600
            },
            NotificationType.INFO: {
                'background': ft.Colors.BLUE_50,
                'border': ft.Colors.BLUE_200,
                'icon': ft.Colors.BLUE_600,
                'text': ft.Colors.BLUE_800,
                'action': ft.Colors.BLUE_600
            }
        }
        
        return color_schemes.get(notification_type, color_schemes[NotificationType.INFO])
    
    def _get_notification_icon(self, notification_type: NotificationType) -> str:
        """Get icon for notification type."""
        icons = {
            NotificationType.SUCCESS: ft.Icons.CHECK_CIRCLE,
            NotificationType.ERROR: ft.Icons.ERROR,
            NotificationType.WARNING: ft.Icons.WARNING,
            NotificationType.INFO: ft.Icons.INFO
        }
        
        return icons.get(notification_type, ft.Icons.INFO)
    
    def dismiss_notification(self, notification_id: str):
        """Dismiss a specific notification."""
        if self.container and notification_id in self.notifications:
            # Find and remove the notification
            for control in self.container.controls[:]:
                if hasattr(control, 'data') and control.data == notification_id:
                    self.container.controls.remove(control)
                    break
            
            self.notifications.remove(notification_id)
            self.page.update()
    
    def _remove_oldest_notification(self):
        """Remove the oldest notification."""
        if self.notifications:
            oldest_id = self.notifications[0]
            self.dismiss_notification(oldest_id)
    
    async def _auto_dismiss_notification(self, notification_id: str, delay: float):
        """Auto dismiss notification after delay."""
        await asyncio.sleep(delay)
        self.dismiss_notification(notification_id)
    
    def _handle_action(self, action_callback: Callable, notification_id: str):
        """Handle notification action."""
        try:
            action_callback()
        except Exception:
            pass  # Ignore action errors
        
        # Dismiss notification after action
        self.dismiss_notification(notification_id)
    
    def clear_all(self):
        """Clear all notifications."""
        if self.container:
            self.container.controls.clear()
            self.notifications.clear()
            self.page.update()
    
    # Convenience methods
    def show_success(self, message: str, title: str = "Success", **kwargs):
        """Show success notification."""
        return self.show_notification(message, NotificationType.SUCCESS, title, **kwargs)
    
    def show_error(self, message: str, title: str = "Error", **kwargs):
        """Show error notification."""
        return self.show_notification(message, NotificationType.ERROR, title, **kwargs)
    
    def show_warning(self, message: str, title: str = "Warning", **kwargs):
        """Show warning notification."""
        return self.show_notification(message, NotificationType.WARNING, title, **kwargs)
    
    def show_info(self, message: str, title: str = "Information", **kwargs):
        """Show info notification."""
        return self.show_notification(message, NotificationType.INFO, title, **kwargs)
