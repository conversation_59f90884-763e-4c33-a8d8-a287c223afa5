"""
Tooltip System
==============

Advanced tooltip system for enhanced user experience.
"""

import flet as ft
from typing import Optional, Dict, Any
from enum import Enum


class TooltipPosition(Enum):
    """Tooltip position enumeration."""
    TOP = "top"
    BOTTOM = "bottom"
    LEFT = "left"
    RIGHT = "right"
    AUTO = "auto"


class TooltipSystem:
    """Advanced tooltip system."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.active_tooltips: Dict[str, ft.Container] = {}
        self.tooltip_styles = {
            'default': {
                'bgcolor': ft.Colors.GREY_800,
                'color': ft.Colors.WHITE,
                'border_radius': 6,
                'padding': 8,
                'max_width': 250
            },
            'info': {
                'bgcolor': ft.Colors.BLUE_600,
                'color': ft.Colors.WHITE,
                'border_radius': 6,
                'padding': 8,
                'max_width': 250
            },
            'warning': {
                'bgcolor': ft.Colors.ORANGE_600,
                'color': ft.Colors.WHITE,
                'border_radius': 6,
                'padding': 8,
                'max_width': 250
            },
            'error': {
                'bgcolor': ft.Colors.RED_600,
                'color': ft.Colors.WHITE,
                'border_radius': 6,
                'padding': 8,
                'max_width': 250
            }
        }
    
    def create_tooltip_wrapper(self,
                              control: ft.Control,
                              tooltip_text: str,
                              tooltip_type: str = "default",
                              position: TooltipPosition = TooltipPosition.AUTO,
                              rich_content: Optional[ft.Control] = None) -> ft.Container:
        """Create a wrapper with tooltip functionality."""
        
        tooltip_id = f"tooltip_{id(control)}"
        
        def on_hover(e):
            if e.data == "true":
                self._show_tooltip(tooltip_id, tooltip_text, tooltip_type, position, rich_content)
            else:
                self._hide_tooltip(tooltip_id)
        
        # Wrap control in hover-sensitive container
        wrapper = ft.Container(
            content=control,
            on_hover=on_hover,
            data=tooltip_id
        )
        
        return wrapper
    
    def _show_tooltip(self,
                     tooltip_id: str,
                     text: str,
                     tooltip_type: str,
                     position: TooltipPosition,
                     rich_content: Optional[ft.Control]):
        """Show tooltip."""
        
        # Create tooltip content
        if rich_content:
            content = rich_content
        else:
            content = ft.Text(
                text,
                size=12,
                color=self.tooltip_styles[tooltip_type]['color'],
                text_align=ft.TextAlign.LEFT
            )
        
        # Create tooltip container
        tooltip = ft.Container(
            content=content,
            bgcolor=self.tooltip_styles[tooltip_type]['bgcolor'],
            border_radius=self.tooltip_styles[tooltip_type]['border_radius'],
            padding=self.tooltip_styles[tooltip_type]['padding'],
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK26,
                offset=ft.Offset(0, 2)
            ),
            animate_opacity=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT)
        )
        
        # Position tooltip (simplified positioning)
        positioned_tooltip = ft.Container(
            content=tooltip,
            # Position would be calculated based on mouse position in a real implementation
            # For now, using fixed positioning
            top=50,
            left=50
        )
        
        # Add to page overlay
        self.active_tooltips[tooltip_id] = positioned_tooltip
        self.page.overlay.append(positioned_tooltip)
        self.page.update()
    
    def _hide_tooltip(self, tooltip_id: str):
        """Hide tooltip."""
        if tooltip_id in self.active_tooltips:
            tooltip = self.active_tooltips[tooltip_id]
            if tooltip in self.page.overlay:
                self.page.overlay.remove(tooltip)
            del self.active_tooltips[tooltip_id]
            self.page.update()
    
    def hide_all_tooltips(self):
        """Hide all active tooltips."""
        for tooltip_id in list(self.active_tooltips.keys()):
            self._hide_tooltip(tooltip_id)
    
    def create_help_tooltip(self, control: ft.Control, help_text: str) -> ft.Container:
        """Create a help tooltip with info icon."""
        help_icon = ft.IconButton(
            icon=ft.Icons.HELP_OUTLINE,
            icon_size=16,
            tooltip=help_text,
            style=ft.ButtonStyle(
                padding=ft.padding.all(4)
            )
        )
        
        return ft.Row([
            control,
            help_icon
        ], alignment=ft.MainAxisAlignment.START)
    
    def create_info_tooltip(self, text: str, info_text: str) -> ft.Container:
        """Create text with info tooltip."""
        text_control = ft.Text(text, size=14)
        
        return self.create_tooltip_wrapper(
            text_control,
            info_text,
            tooltip_type="info"
        )
    
    def create_validation_tooltip(self, control: ft.Control, 
                                 validation_message: str,
                                 is_error: bool = True) -> ft.Container:
        """Create tooltip for validation messages."""
        tooltip_type = "error" if is_error else "warning"
        
        return self.create_tooltip_wrapper(
            control,
            validation_message,
            tooltip_type=tooltip_type
        )


class HelpSystem:
    """Contextual help system."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.help_content: Dict[str, Dict[str, Any]] = {}
        self.help_overlay: Optional[ft.Container] = None
    
    def register_help_content(self, context: str, title: str, content: str, 
                            links: Optional[Dict[str, str]] = None):
        """Register help content for a specific context."""
        self.help_content[context] = {
            'title': title,
            'content': content,
            'links': links or {}
        }
    
    def show_help(self, context: str):
        """Show help for specific context."""
        if context not in self.help_content:
            return
        
        help_data = self.help_content[context]
        
        # Create help content
        content_controls = [
            ft.Text(help_data['title'], size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            ft.Text(help_data['content'], size=14)
        ]
        
        # Add links if available
        if help_data['links']:
            content_controls.append(ft.Divider())
            content_controls.append(ft.Text("Related Links:", size=14, weight=ft.FontWeight.BOLD))
            
            for link_text, link_url in help_data['links'].items():
                link_button = ft.TextButton(
                    link_text,
                    on_click=lambda _, url=link_url: self._open_link(url)
                )
                content_controls.append(link_button)
        
        # Close button
        close_button = ft.ElevatedButton(
            "Close",
            on_click=lambda _: self.hide_help(),
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE
        )
        content_controls.append(close_button)
        
        # Create help dialog
        help_card = ft.Card(
            content=ft.Container(
                content=ft.Column(content_controls, spacing=10),
                padding=20,
                width=500,
                bgcolor=ft.Colors.WHITE
            ),
            elevation=8
        )
        
        # Create overlay
        self.help_overlay = ft.Container(
            content=ft.Stack([
                ft.Container(
                    bgcolor=ft.Colors.BLACK54,
                    expand=True,
                    on_click=lambda _: self.hide_help()
                ),
                ft.Container(
                    content=help_card,
                    alignment=ft.alignment.center,
                    expand=True
                )
            ]),
            expand=True
        )
        
        self.page.overlay.append(self.help_overlay)
        self.page.update()
    
    def hide_help(self):
        """Hide help overlay."""
        if self.help_overlay and self.help_overlay in self.page.overlay:
            self.page.overlay.remove(self.help_overlay)
            self.help_overlay = None
            self.page.update()
    
    def _open_link(self, url: str):
        """Open external link."""
        # In a real implementation, this would open the URL in a browser
        pass
    
    def create_help_button(self, context: str, icon: str = ft.Icons.HELP) -> ft.IconButton:
        """Create a help button for specific context."""
        return ft.IconButton(
            icon=icon,
            tooltip=f"Help for {context}",
            on_click=lambda _: self.show_help(context)
        )
