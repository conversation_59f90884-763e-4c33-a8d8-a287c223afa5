"""
Progress Indicator Widget
=========================

Enhanced progress indicator component.
"""

import flet as ft
from typing import Optional


class ProgressIndicator:
    """Enhanced progress indicator widget."""
    
    def __init__(self,
                 progress: float = 0.0,
                 max_value: float = 100.0,
                 message: str = "",
                 show_percentage: bool = True,
                 color: str = ft.Colors.BLUE,
                 height: int = 8):
        self.progress = progress
        self.max_value = max_value
        self.message = message
        self.show_percentage = show_percentage
        self.color = color
        self.height = height
    
    def build(self) -> ft.Container:
        """Build the progress indicator."""
        progress_value = min(self.progress / self.max_value, 1.0) if self.max_value > 0 else 0
        percentage = (progress_value * 100)
        
        controls = []
        
        # Message
        if self.message:
            controls.append(
                ft.Text(self.message, size=14, weight=ft.FontWeight.BOLD)
            )
        
        # Progress bar
        progress_bar = ft.ProgressBar(
            value=progress_value,
            color=self.color,
            bgcolor=ft.Colors.GREY_300,
            height=self.height
        )
        controls.append(progress_bar)
        
        # Percentage text
        if self.show_percentage:
            controls.append(
                ft.Text(f"{percentage:.1f}%", 
                       size=12, 
                       color=ft.Colors.GREY_600,
                       text_align=ft.TextAlign.CENTER)
            )
        
        return ft.Container(
            content=ft.Column(
                controls,
                spacing=5,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=10
        )
    
    def update_progress(self, progress: float, message: str = ""):
        """Update progress value and message."""
        self.progress = progress
        if message:
            self.message = message


class CircularProgressIndicator:
    """Circular progress indicator widget."""
    
    def __init__(self,
                 progress: float = 0.0,
                 max_value: float = 100.0,
                 size: int = 60,
                 stroke_width: int = 4,
                 color: str = ft.Colors.BLUE):
        self.progress = progress
        self.max_value = max_value
        self.size = size
        self.stroke_width = stroke_width
        self.color = color
    
    def build(self) -> ft.Container:
        """Build the circular progress indicator."""
        progress_value = min(self.progress / self.max_value, 1.0) if self.max_value > 0 else 0
        percentage = (progress_value * 100)
        
        return ft.Container(
            content=ft.Stack([
                ft.ProgressRing(
                    value=progress_value,
                    stroke_width=self.stroke_width,
                    color=self.color,
                    width=self.size,
                    height=self.size
                ),
                ft.Container(
                    content=ft.Text(f"{percentage:.0f}%", 
                                   size=12, 
                                   weight=ft.FontWeight.BOLD,
                                   text_align=ft.TextAlign.CENTER),
                    width=self.size,
                    height=self.size,
                    alignment=ft.alignment.center
                )
            ]),
            width=self.size,
            height=self.size
        )
