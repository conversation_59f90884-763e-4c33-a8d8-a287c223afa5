"""
Export Configuration
====================

Configuration settings for export functionality.
"""

from dataclasses import dataclass
from typing import Dict, Any, List
from pathlib import Path


@dataclass
class ExportConfig:
    """Export configuration settings."""
    
    # Default export formats
    default_formats: List[str] = None
    
    # File naming
    include_timestamp: bool = True
    include_client_name: bool = True
    timestamp_format: str = "%Y%m%d_%H%M%S"
    
    # Excel export settings
    excel_include_charts: bool = True
    excel_include_metadata: bool = True
    excel_sheet_names: Dict[str, str] = None
    
    # DOCX export settings
    docx_include_charts: bool = True
    docx_include_validation: bool = True
    docx_template_path: str = ""
    
    # HTML export settings
    html_include_css: bool = True
    html_responsive: bool = True
    html_include_charts: bool = True
    
    # Chart export settings
    chart_format: str = "PNG"
    chart_dpi: int = 100
    chart_width: int = 800
    chart_height: int = 600
    
    # Directory structure
    create_date_folders: bool = True
    create_client_folders: bool = True
    folder_structure: str = "{date}/{client_name}"
    
    def __post_init__(self):
        """Initialize default values."""
        if self.default_formats is None:
            self.default_formats = ["Excel", "DOCX", "HTML"]
        
        if self.excel_sheet_names is None:
            self.excel_sheet_names = {
                'assumptions': 'Project Assumptions',
                'cashflow': 'Cashflow Analysis',
                'kpis': 'Key Performance Indicators',
                'sensitivity': 'Sensitivity Analysis',
                'monte_carlo': 'Monte Carlo Results',
                'scenarios': 'Scenario Analysis',
                'validation': 'Model Validation',
                'metadata': 'Project Metadata'
            }
    
    def get_excel_settings(self) -> Dict[str, Any]:
        """Get Excel export settings."""
        return {
            'include_charts': self.excel_include_charts,
            'include_metadata': self.excel_include_metadata,
            'sheet_names': self.excel_sheet_names
        }
    
    def get_docx_settings(self) -> Dict[str, Any]:
        """Get DOCX export settings."""
        return {
            'include_charts': self.docx_include_charts,
            'include_validation': self.docx_include_validation,
            'template_path': self.docx_template_path
        }
    
    def get_html_settings(self) -> Dict[str, Any]:
        """Get HTML export settings."""
        return {
            'include_css': self.html_include_css,
            'responsive': self.html_responsive,
            'include_charts': self.html_include_charts
        }
    
    def get_chart_settings(self) -> Dict[str, Any]:
        """Get chart export settings."""
        return {
            'format': self.chart_format,
            'dpi': self.chart_dpi,
            'width': self.chart_width,
            'height': self.chart_height
        }
    
    def get_filename_template(self, file_type: str) -> str:
        """Get filename template for file type."""
        templates = {
            'excel': 'financial_model_{timestamp}.xlsx',
            'docx': 'financial_report_{timestamp}.docx',
            'html': 'financial_report_{timestamp}.html',
            'json': 'financial_data_{timestamp}.json'
        }
        return templates.get(file_type.lower(), f'{file_type.lower()}_{{{self.timestamp_format}}}.{file_type.lower()}')
    
    def get_folder_structure_template(self) -> str:
        """Get folder structure template."""
        return self.folder_structure
    
    def should_create_date_folders(self) -> bool:
        """Check if date folders should be created."""
        return self.create_date_folders
    
    def should_create_client_folders(self) -> bool:
        """Check if client folders should be created."""
        return self.create_client_folders
    
    def get_timestamp_format(self) -> str:
        """Get timestamp format."""
        return self.timestamp_format
