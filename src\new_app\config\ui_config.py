"""
UI Configuration
================

User interface configuration settings.
"""

from dataclasses import dataclass
from typing import Dict, Any
import flet as ft


@dataclass
class UIConfig:
    """UI configuration settings."""
    
    # Theme settings
    theme_mode: str = "light"
    primary_color: str = ft.Colors.BLUE
    secondary_color: str = ft.Colors.ORANGE
    success_color: str = ft.Colors.GREEN
    error_color: str = ft.Colors.RED
    warning_color: str = ft.Colors.ORANGE
    
    # Layout settings
    page_padding: int = 20
    card_padding: int = 15
    section_spacing: int = 20
    
    # Typography
    title_size: int = 24
    subtitle_size: int = 18
    body_size: int = 14
    caption_size: int = 12
    
    # Component sizes
    button_height: int = 40
    input_height: int = 56
    card_elevation: int = 2
    
    # Animation settings
    enable_animations: bool = True
    animation_duration: int = 300
    
    # Chart settings
    chart_height: int = 300
    chart_width: int = 400
    chart_colors: list = None
    
    # Navigation
    navigation_rail_width: int = 100
    navigation_rail_extended_width: int = 200
    
    def __post_init__(self):
        """Initialize default values."""
        if self.chart_colors is None:
            self.chart_colors = [
                ft.Colors.BLUE,
                ft.Colors.ORANGE,
                ft.Colors.GREEN,
                ft.Colors.RED,
                ft.Colors.PURPLE,
                ft.Colors.TEAL,
                ft.Colors.PINK,
                ft.Colors.INDIGO
            ]
    
    def get_theme_data(self) -> Dict[str, Any]:
        """Get theme data for Flet."""
        return {
            'color_scheme_seed': self.primary_color,
            'use_material3': True
        }
    
    def get_page_settings(self) -> Dict[str, Any]:
        """Get page settings."""
        return {
            'theme_mode': ft.ThemeMode.LIGHT if self.theme_mode == "light" else ft.ThemeMode.DARK,
            'padding': self.page_padding
        }
    
    def get_card_style(self) -> Dict[str, Any]:
        """Get card styling."""
        return {
            'elevation': self.card_elevation,
            'padding': self.card_padding
        }
    
    def get_button_style(self) -> Dict[str, Any]:
        """Get button styling."""
        return {
            'height': self.button_height,
            'style': ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        }
    
    def get_text_styles(self) -> Dict[str, Dict[str, Any]]:
        """Get text styles."""
        return {
            'title': {
                'size': self.title_size,
                'weight': ft.FontWeight.BOLD
            },
            'subtitle': {
                'size': self.subtitle_size,
                'weight': ft.FontWeight.BOLD
            },
            'body': {
                'size': self.body_size
            },
            'caption': {
                'size': self.caption_size,
                'color': ft.Colors.GREY_600
            }
        }
    
    def get_color_scheme(self) -> Dict[str, str]:
        """Get color scheme."""
        return {
            'primary': self.primary_color,
            'secondary': self.secondary_color,
            'success': self.success_color,
            'error': self.error_color,
            'warning': self.warning_color
        }
    
    def get_chart_config(self) -> Dict[str, Any]:
        """Get chart configuration."""
        return {
            'default_height': self.chart_height,
            'default_width': self.chart_width,
            'colors': self.chart_colors,
            'enable_animations': self.enable_animations
        }
    
    def get_navigation_config(self) -> Dict[str, Any]:
        """Get navigation configuration."""
        return {
            'rail_width': self.navigation_rail_width,
            'rail_extended_width': self.navigation_rail_extended_width
        }
