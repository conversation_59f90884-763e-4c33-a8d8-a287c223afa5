# Enhanced Financial Model - User Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Application Overview](#application-overview)
3. [Project Setup](#project-setup)
4. [Financial Analysis](#financial-analysis)
5. [Advanced Features](#advanced-features)
6. [Export and Reporting](#export-and-reporting)
7. [Tips and Best Practices](#tips-and-best-practices)

## Getting Started

### First Launch

1. **Start the Application**
   ```bash
   python src/new_app/main.py
   ```

2. **Security Screen**
   - Enter password: `agevolami2024`
   - The security screen displays developer and company information

3. **Main Interface**
   - Navigation rail on the left
   - Main content area in the center
   - Status bar at the bottom

### Interface Overview

The application uses a tab-based navigation system:

- **Setup**: Project configuration and client information
- **Dashboard**: Main financial analysis overview
- **Locations**: Location comparison analysis
- **Financial**: Detailed financial model results
- **Validation**: Model validation and benchmarks
- **Sensitivity**: Sensitivity analysis
- **Monte Carlo**: Risk analysis through simulation
- **Scenarios**: Scenario comparison
- **Export**: Report generation and export

## Application Overview

### Key Features

- **Comprehensive Financial Modeling**: DCF analysis with industry-standard KPIs
- **Location Comparison**: Multi-location analysis for optimal project placement
- **Risk Analysis**: Monte Carlo simulation and sensitivity analysis
- **Professional Reporting**: Export to Excel, DOCX, HTML, and JSON
- **Validation & Benchmarking**: Industry benchmark comparison
- **Grant Analysis**: Detailed analysis of various grant schemes

### Workflow

1. **Setup**: Configure client profile and project parameters
2. **Analysis**: Run financial model and additional analyses
3. **Review**: Examine results across different views
4. **Export**: Generate professional reports

## Project Setup

### Client Profile

Navigate to the **Setup** tab to configure client information:

**Required Fields:**
- Company Name
- Client Name  
- Project Name

**Optional Fields:**
- Contact Email
- Phone Number
- Project Location
- Project Capacity (MW)
- Preferred Currency

**Tips:**
- Complete all fields for professional reports
- Email validation is automatic
- Phone number accepts international formats

### Project Parameters

Configure technical and financial parameters:

**Technical Parameters:**
- **Capacity (MW)**: Project size in megawatts
- **Production Year 1 (MWh)**: Expected first-year energy production
- **Degradation Rate (%)**: Annual performance degradation
- **Project Life (years)**: Total project duration

**Financial Parameters:**
- **CAPEX (M EUR)**: Capital expenditure
- **OPEX Year 1 (k EUR)**: First-year operational expenditure
- **PPA Price (EUR/kWh)**: Power purchase agreement price
- **Debt Ratio**: Percentage of debt financing
- **Interest Rate (%)**: Cost of debt
- **Discount Rate (%)**: Required rate of return

**Grant Parameters:**
- **Italian Grant**: Italian government incentives
- **MASEN Grant**: Moroccan renewable energy grants
- **Connection Grant**: Grid connection support
- **SIMEST African Fund**: International development funding

### Validation

The application automatically validates all inputs:

- **Green checkmark**: Valid parameter
- **Orange warning**: Parameter outside typical range
- **Red error**: Invalid parameter requiring correction

## Financial Analysis

### Running the Model

1. **Complete Setup**: Ensure all required fields are filled
2. **Validate Parameters**: Check for validation errors
3. **Run Analysis**: Click "Run Model" or "Generate Complete Analysis & Reports"

### Key Performance Indicators (KPIs)

The model calculates industry-standard KPIs:

- **Project IRR**: Internal rate of return for the entire project
- **Equity IRR**: Internal rate of return for equity investors
- **NPV Project**: Net present value of the project
- **NPV Equity**: Net present value for equity investors
- **LCOE**: Levelized cost of energy
- **Min DSCR**: Minimum debt service coverage ratio
- **Payback Period**: Time to recover initial investment

### Dashboard View

The dashboard provides a comprehensive overview:

- **KPI Summary Cards**: Visual representation of key metrics
- **Financial Summary**: Revenue, costs, and grants breakdown
- **Charts**: Interactive visualizations
- **Grant Analysis**: Detailed grant impact analysis
- **Risk Assessment**: Automated risk evaluation

## Advanced Features

### Location Comparison

Compare multiple Moroccan locations:

1. **Navigate to Locations tab**
2. **Select Locations**: Choose from 9 available locations
3. **Run Comparison**: Click "Run Comparison"
4. **Review Results**: Rankings, recommendations, and detailed analysis

**Available Locations:**
- Ouarzazate, Dakhla, Tarfaya, Noor Midelt
- Laâyoune, Tan-Tan, Boujdour, Tata, Zagora

### Sensitivity Analysis

Analyze impact of key variables:

1. **Navigate to Sensitivity tab**
2. **Select Variables**: Choose parameters to analyze
3. **Run Analysis**: Generate sensitivity results
4. **Review Impact**: Understand which variables most affect returns

**Common Variables:**
- Production (MWh)
- PPA Price
- CAPEX
- OPEX
- Discount Rate

### Monte Carlo Simulation

Perform risk analysis through probabilistic modeling:

1. **Navigate to Monte Carlo tab**
2. **Set Simulations**: Choose number of simulations (1000-10000)
3. **Run Simulation**: Execute Monte Carlo analysis
4. **Review Results**: Statistical distribution of outcomes

### Scenario Analysis

Compare different project scenarios:

1. **Navigate to Scenarios tab**
2. **Select Scenarios**: Choose from predefined scenarios
3. **Run Analysis**: Generate scenario comparison
4. **Review Results**: Compare outcomes across scenarios

**Available Scenarios:**
- Base Case
- Optimistic
- Pessimistic
- High Grants
- No Grants

## Export and Reporting

### Quick Export

For immediate results:

1. **Navigate to Export tab**
2. **Select Formats**: Choose Excel, DOCX, HTML, or JSON
3. **Click Quick Export**: Generate basic reports

### Comprehensive Export

For complete analysis:

1. **Use "Generate Complete Analysis & Reports"** from Setup tab
2. **Automatic Generation**: All analyses run automatically
3. **Multiple Formats**: Reports generated in selected formats
4. **Professional Quality**: Charts, validation, and detailed analysis included

### Export Formats

**Excel Reports:**
- Multiple worksheets
- Embedded charts
- Formatted tables
- Metadata sheet

**DOCX Reports:**
- Professional document layout
- Embedded charts and tables
- Executive summary
- Detailed analysis sections

**HTML Reports:**
- Web-ready format
- Interactive elements
- Responsive design
- Easy sharing

**JSON Data:**
- Raw data export
- API integration ready
- Complete dataset
- Programmatic access

### File Organization

Exported files are organized by:
- **Date**: YYYY-MM-DD format
- **Client Name**: Clean company name
- **File Type**: Separate folders for different formats

Example structure:
```
outputs/
├── 2024-01-15/
│   └── Test_Solar_Company/
│       ├── data/
│       ├── charts/
│       └── reports/
```

## Tips and Best Practices

### Data Entry

- **Use Realistic Values**: Base parameters on actual project data
- **Check Units**: Ensure correct units (MW, MWh, EUR, %)
- **Validate Regularly**: Address validation warnings promptly
- **Save Configurations**: Export JSON for project backup

### Analysis

- **Start with Base Case**: Establish baseline before variations
- **Use Location Comparison**: Optimize project placement
- **Run Sensitivity Analysis**: Identify key risk factors
- **Validate Results**: Check against industry benchmarks

### Reporting

- **Complete Client Profile**: Ensures professional reports
- **Use Comprehensive Export**: Generates all analyses
- **Review Before Sharing**: Check all calculations and charts
- **Maintain File Organization**: Use consistent naming conventions

### Performance

- **Limit Monte Carlo Simulations**: Use 1000-5000 for faster results
- **Close Unused Tabs**: Improves application performance
- **Regular Cleanup**: Remove old export files periodically

### Troubleshooting

**Common Issues:**

1. **Validation Errors**: Check parameter ranges and units
2. **Export Failures**: Ensure sufficient disk space
3. **Slow Performance**: Reduce simulation counts
4. **Display Issues**: Check screen resolution and scaling

**Getting Help:**

- Check validation messages for specific guidance
- Review log files for detailed error information
- Refer to deployment guide for technical issues

## Contact and Support

For questions, support, or customization requests:

**Abdelhalim Serhani**  
Financial & Business Consultant  
Agevolami SRL  
Website: www.agevolami.it & www.agevolami.ma  

*"Your way to explore crossborder opportunities and grow big"*

---

**Application Version**: 2.0.0  
**Last Updated**: 2024  
**Author**: Abdelhalim Serhani, Agevolami SRL
