"""
Enhanced Financial Model Application
====================================

Main entry point for the enhanced financial modeling application.

Author: Abdelhalim Serhani
Company: Agevolami SRL
Website: www.agevolami.it & www.agevolami.ma
Tagline: Your way to explore crossborder opportunities and grow big
"""

import flet as ft
import logging
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from new_app.app.app_controller import AppController
from new_app.config.app_config import AppConfig
from new_app.config.ui_config import UIConfig


def setup_logging():
    """Setup application logging."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def create_security_screen(page: ft.Page, on_authenticated: callable):
    """Create security/authentication screen."""
    
    def on_password_submit(e):
        password = password_field.value
        # Simple password check - in production, use proper authentication
        if password == "agevolami2024":
            on_authenticated()
        else:
            error_text.value = "Invalid password. Please try again."
            error_text.visible = True
            page.update()
    
    def on_password_change(e):
        error_text.visible = False
        page.update()
    
    # Password field
    password_field = ft.TextField(
        label="Enter Password",
        password=True,
        autofocus=True,
        on_submit=on_password_submit,
        on_change=on_password_change,
        width=300
    )
    
    # Error text
    error_text = ft.Text(
        "Invalid password. Please try again.",
        color=ft.Colors.RED,
        visible=False
    )
    
    # Login button
    login_button = ft.ElevatedButton(
        "Access Application",
        icon=ft.Icons.LOGIN,
        on_click=on_password_submit,
        bgcolor=ft.Colors.BLUE_600,
        color=ft.Colors.WHITE,
        width=300,
        height=50
    )
    
    # Developer info
    developer_info = ft.Container(
        content=ft.Column([
            ft.Text("🚀 Enhanced Financial Model v2.0", 
                   size=28, weight=ft.FontWeight.BOLD, 
                   text_align=ft.TextAlign.CENTER),
            ft.Text("Professional Solar PV Project Financial Analysis", 
                   size=16, color=ft.Colors.GREY_700,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=30),
            
            # Developer signature
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.PERSON, color=ft.Colors.BLUE_600, size=30),
                        ft.Column([
                            ft.Text("Abdelhalim Serhani", 
                                   size=18, weight=ft.FontWeight.BOLD),
                            ft.Text("Financial & Business Consultant", 
                                   size=14, color=ft.Colors.GREY_600)
                        ], spacing=2)
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    
                    ft.Row([
                        ft.Icon(ft.Icons.BUSINESS, color=ft.Colors.GREEN_600, size=20),
                        ft.Text("Agevolami.it/ma", size=14, weight=ft.FontWeight.BOLD)
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    
                    ft.Text("Your way to explore crossborder opportunities and grow big", 
                           size=12, color=ft.Colors.BLUE_700, 
                           text_align=ft.TextAlign.CENTER,
                           italic=True),
                    
                    ft.Row([
                        ft.Icon(ft.Icons.WEB, color=ft.Colors.ORANGE_600, size=16),
                        ft.Text("www.agevolami.it & www.agevolami.ma", 
                               size=12, color=ft.Colors.GREY_600)
                    ], alignment=ft.MainAxisAlignment.CENTER)
                ], spacing=10),
                padding=20,
                bgcolor=ft.Colors.BLUE_50,
                border_radius=10,
                border=ft.border.all(1, ft.Colors.BLUE_200)
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=40
    )
    
    # Main security screen layout
    security_content = ft.Container(
        content=ft.Column([
            developer_info,
            ft.Divider(height=40),
            
            # Login section
            ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.SECURITY, size=60, color=ft.Colors.BLUE_600),
                    ft.Text("Secure Access Required", 
                           size=20, weight=ft.FontWeight.BOLD),
                    ft.Text("Please enter the access password to continue", 
                           size=14, color=ft.Colors.GREY_600),
                    ft.Divider(height=20),
                    password_field,
                    error_text,
                    ft.Divider(height=10),
                    login_button
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=30,
                bgcolor=ft.Colors.WHITE,
                border_radius=15,
                border=ft.border.all(1, ft.Colors.GREY_300),
                shadow=ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=10,
                    color=ft.Colors.GREY_400,
                    offset=ft.Offset(0, 2)
                )
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        alignment=ft.alignment.center,
        expand=True,
        bgcolor=ft.Colors.GREY_50
    )
    
    page.add(security_content)


def main(page: ft.Page):
    """Main application function."""
    
    # Load configuration
    app_config = AppConfig()
    ui_config = UIConfig()
    
    # Configure page
    page.title = f"{app_config.app_name} - {app_config.company_name}"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1400
    page.window_height = 900
    page.window_min_width = 1200
    page.window_min_height = 800
    page.padding = 0
    
    def start_main_application():
        """Start the main application after authentication."""
        page.clean()
        
        # Initialize and start the main application controller
        app_controller = AppController(page)
        
        logging.info("Enhanced Financial Model Application started successfully")
    
    # Show security screen first
    create_security_screen(page, start_main_application)


if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    # Log application startup
    logging.info("Starting Enhanced Financial Model Application")
    logging.info("Author: Abdelhalim Serhani")
    logging.info("Company: Agevolami SRL")
    logging.info("Website: www.agevolami.it & www.agevolami.ma")
    
    try:
        # Start the Flet application
        ft.app(
            target=main,
            name="Enhanced Financial Model",
            assets_dir="assets"
        )
    except Exception as e:
        logging.error(f"Failed to start application: {str(e)}")
        sys.exit(1)
