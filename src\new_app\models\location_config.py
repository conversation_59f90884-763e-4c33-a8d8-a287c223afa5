"""
Location Configuration Data Models
==================================

Data models for location configurations and comparison functionality.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import json
from pathlib import Path


@dataclass
class LocationConfig:
    """Configuration for a specific project location."""
    
    name: str
    production_mwh_year1: float
    capex_meur: float
    opex_keuros_year1: float
    ppa_price_eur_kwh: float
    land_lease_eur_mw_year: float
    description: str
    advantages: List[str] = field(default_factory=list)
    challenges: List[str] = field(default_factory=list)
    irradiation_kwh_m2: float = 2000.0
    
    # Additional technical parameters
    grid_connection_cost_meur: float = 0.0
    transmission_losses_percent: float = 2.0
    availability_factor: float = 0.98
    
    # Environmental and regulatory factors
    environmental_impact_score: float = 5.0  # 1-10 scale
    regulatory_complexity_score: float = 5.0  # 1-10 scale
    permitting_time_months: int = 12
    
    def __post_init__(self):
        """Validate data after initialization."""
        if self.production_mwh_year1 <= 0:
            raise ValueError("Production must be positive")
        if self.capex_meur <= 0:
            raise ValueError("CAPEX must be positive")
        if not 0 <= self.ppa_price_eur_kwh <= 1:
            raise ValueError("PPA price must be between 0 and 1 EUR/kWh")
    
    def calculate_capacity_factor(self, capacity_mw: float) -> float:
        """Calculate capacity factor based on production and capacity."""
        if capacity_mw <= 0:
            return 0.0
        
        max_annual_production = capacity_mw * 8760  # MWh
        return self.production_mwh_year1 / max_annual_production
    
    def get_lcoe_estimate(self, discount_rate: float = 0.08, project_life: int = 25) -> float:
        """Get rough LCOE estimate for comparison."""
        # Simplified LCOE calculation for comparison
        annual_opex = self.opex_keuros_year1 * 1000
        total_capex = self.capex_meur * 1e6
        
        # Present value of costs
        pv_opex = sum(annual_opex / (1 + discount_rate) ** year for year in range(1, project_life + 1))
        pv_costs = total_capex + pv_opex
        
        # Present value of energy
        annual_degradation = 0.005  # 0.5% per year
        pv_energy = 0
        for year in range(1, project_life + 1):
            energy_year = self.production_mwh_year1 * (1 - annual_degradation) ** (year - 1) * 1000  # kWh
            pv_energy += energy_year / (1 + discount_rate) ** year
        
        return pv_costs / pv_energy if pv_energy > 0 else float('inf')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'production_mwh_year1': self.production_mwh_year1,
            'capex_meur': self.capex_meur,
            'opex_keuros_year1': self.opex_keuros_year1,
            'ppa_price_eur_kwh': self.ppa_price_eur_kwh,
            'land_lease_eur_mw_year': self.land_lease_eur_mw_year,
            'description': self.description,
            'advantages': self.advantages,
            'challenges': self.challenges,
            'irradiation_kwh_m2': self.irradiation_kwh_m2,
            'grid_connection_cost_meur': self.grid_connection_cost_meur,
            'transmission_losses_percent': self.transmission_losses_percent,
            'availability_factor': self.availability_factor,
            'environmental_impact_score': self.environmental_impact_score,
            'regulatory_complexity_score': self.regulatory_complexity_score,
            'permitting_time_months': self.permitting_time_months
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LocationConfig':
        """Create instance from dictionary."""
        return cls(**{k: v for k, v in data.items() if k in cls.__dataclass_fields__})


class LocationManager:
    """Manager for location configurations and comparisons."""
    
    def __init__(self):
        self.locations: Dict[str, LocationConfig] = {}
        self._load_default_locations()
    
    def _load_default_locations(self):
        """Load default location configurations."""
        default_locations = {
            "Ouarzazate": LocationConfig(
                name="Ouarzazate",
                production_mwh_year1=18000,
                capex_meur=8.5,
                opex_keuros_year1=180,
                ppa_price_eur_kwh=0.045,
                land_lease_eur_mw_year=2000,
                description="Base reference location with established infrastructure",
                advantages=["Established solar complex", "Grid connection", "Experience with solar projects"],
                challenges=["Moderate solar resource", "Competition for land"],
                irradiation_kwh_m2=2200
            ),
            "Dakhla": LocationConfig(
                name="Dakhla",
                production_mwh_year1=18600,
                capex_meur=8.7,
                opex_keuros_year1=185,
                ppa_price_eur_kwh=0.043,
                land_lease_eur_mw_year=2200,
                description="Excellent solar resource with high wind potential",
                advantages=["Higher solar irradiation", "Coastal location", "Lower PPA prices accepted"],
                challenges=["Remote location", "Grid connection costs", "Limited infrastructure"],
                irradiation_kwh_m2=2380
            ),
            "Tarfaya": LocationConfig(
                name="Tarfaya",
                production_mwh_year1=17600,
                capex_meur=8.3,
                opex_keuros_year1=175,
                ppa_price_eur_kwh=0.044,
                land_lease_eur_mw_year=1900,
                description="Good coastal location with wind synergies",
                advantages=["Coastal location", "Wind synergies", "Lower CAPEX"],
                challenges=["Moderate solar resource", "Seasonal variations"],
                irradiation_kwh_m2=2150
            ),
            "Noor Midelt": LocationConfig(
                name="Noor Midelt",
                production_mwh_year1=17300,
                capex_meur=8.8,
                opex_keuros_year1=190,
                ppa_price_eur_kwh=0.046,
                land_lease_eur_mw_year=2100,
                description="Mountain location with CSP potential",
                advantages=["CSP synergies", "Good infrastructure", "Government support"],
                challenges=["Higher altitude", "Temperature variations", "Higher CAPEX"],
                irradiation_kwh_m2=2100
            ),
            "Laâyoune": LocationConfig(
                name="Laâyoune",
                production_mwh_year1=18200,
                capex_meur=8.4,
                opex_keuros_year1=180,
                ppa_price_eur_kwh=0.043,
                land_lease_eur_mw_year=2000,
                description="Excellent southern location with high irradiation",
                advantages=["High solar irradiation", "Strategic location", "Government priority"],
                challenges=["Remote location", "Limited infrastructure", "Security considerations"],
                irradiation_kwh_m2=2320
            )
        }
        
        for name, config in default_locations.items():
            self.locations[name] = config
    
    def get_location(self, name: str) -> Optional[LocationConfig]:
        """Get location configuration by name."""
        return self.locations.get(name)
    
    def get_all_locations(self) -> Dict[str, LocationConfig]:
        """Get all location configurations."""
        return self.locations.copy()
    
    def get_location_names(self) -> List[str]:
        """Get list of all location names."""
        return list(self.locations.keys())
    
    def add_location(self, location: LocationConfig):
        """Add a new location configuration."""
        self.locations[location.name] = location
    
    def remove_location(self, name: str) -> bool:
        """Remove a location configuration."""
        if name in self.locations:
            del self.locations[name]
            return True
        return False
    
    def compare_locations(self, location_names: List[str]) -> Dict[str, LocationConfig]:
        """Get configurations for comparison."""
        return {name: self.locations[name] for name in location_names if name in self.locations}
    
    def save_to_file(self, filepath: Path):
        """Save location configurations to file."""
        data = {name: config.to_dict() for name, config in self.locations.items()}
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_from_file(self, filepath: Path):
        """Load location configurations from file."""
        if filepath.exists():
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.locations = {}
            for name, config_data in data.items():
                self.locations[name] = LocationConfig.from_dict(config_data)
