"""
Enhanced Project Assumptions Data Model
=======================================

Enhanced data model for project assumptions with validation and calculations.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from core.enhanced_data_models import EnhancedProjectAssumptions as CoreAssumptions


@dataclass
class EnhancedProjectAssumptions(CoreAssumptions):
    """Enhanced project assumptions with additional validation and methods."""
    
    # UI-specific fields
    is_validated: bool = False
    validation_errors: Dict[str, str] = field(default_factory=dict)
    last_modified: Optional[str] = None
    
    def __post_init__(self):
        """Enhanced post-initialization with validation."""
        super().__post_init__()
        self.validate_all()
    
    def validate_all(self) -> Dict[str, str]:
        """Comprehensive validation of all parameters."""
        errors = {}
        
        # Basic parameter validation
        if self.capacity_mw <= 0:
            errors['capacity_mw'] = "Capacity must be positive"
        elif self.capacity_mw > 1000:
            errors['capacity_mw'] = "Capacity seems unreasonably high (>1000 MW)"
        
        if self.project_life_years < 10 or self.project_life_years > 50:
            errors['project_life_years'] = "Project life should be between 10-50 years"
        
        if self.capex_meur <= 0:
            errors['capex_meur'] = "CAPEX must be positive"
        elif self.capex_meur / self.capacity_mw > 2.0:
            errors['capex_meur'] = "CAPEX per MW seems high (>2.0 M EUR/MW)"
        
        # Financial validation
        if not 0 < self.debt_ratio < 1:
            errors['debt_ratio'] = "Debt ratio must be between 0 and 1"
        
        if self.interest_rate < 0 or self.interest_rate > 0.2:
            errors['interest_rate'] = "Interest rate should be between 0-20%"
        
        if self.discount_rate < 0 or self.discount_rate > 0.3:
            errors['discount_rate'] = "Discount rate should be between 0-30%"
        
        if self.debt_years > self.project_life_years:
            errors['debt_years'] = "Debt tenor cannot exceed project life"
        
        # Revenue validation
        if self.production_mwh_year1 <= 0:
            errors['production_mwh_year1'] = "Production must be positive"
        
        capacity_factor = self.production_mwh_year1 / (self.capacity_mw * 8760)
        if capacity_factor > 0.6:
            errors['production_mwh_year1'] = "Capacity factor seems high (>60%)"
        elif capacity_factor < 0.1:
            errors['production_mwh_year1'] = "Capacity factor seems low (<10%)"
        
        if self.ppa_price_eur_kwh <= 0 or self.ppa_price_eur_kwh > 0.5:
            errors['ppa_price_eur_kwh'] = "PPA price should be between 0-0.5 EUR/kWh"
        
        # Grant validation
        total_grants = self.calculate_total_grants()
        if total_grants > self.capex_meur:
            errors['grants'] = "Total grants cannot exceed CAPEX"
        
        self.validation_errors = errors
        self.is_validated = len(errors) == 0
        
        return errors
    
    def calculate_total_grants(self) -> float:
        """Calculate total grants from all sources."""
        return (self.grant_meur_italy + 
                self.grant_meur_masen + 
                self.grant_meur_connection +
                getattr(self, 'grant_meur_simest_africa', 0.0))
    
    def calculate_grant_percentage(self) -> float:
        """Calculate grant percentage of CAPEX."""
        if self.capex_meur <= 0:
            return 0.0
        return (self.calculate_total_grants() / self.capex_meur) * 100
    
    def calculate_capacity_factor(self) -> float:
        """Calculate capacity factor."""
        if self.capacity_mw <= 0:
            return 0.0
        return self.production_mwh_year1 / (self.capacity_mw * 8760)
    
    def calculate_specific_capex(self) -> float:
        """Calculate specific CAPEX (EUR/kW)."""
        if self.capacity_mw <= 0:
            return 0.0
        return (self.capex_meur * 1e6) / (self.capacity_mw * 1000)
    
    def calculate_specific_opex(self) -> float:
        """Calculate specific OPEX (EUR/kW/year)."""
        if self.capacity_mw <= 0:
            return 0.0
        return (self.opex_keuros_year1 * 1000) / (self.capacity_mw * 1000)
    
    def get_financial_summary(self) -> Dict[str, Any]:
        """Get financial summary for display."""
        return {
            'capacity_mw': self.capacity_mw,
            'capex_meur': self.capex_meur,
            'specific_capex_eur_kw': self.calculate_specific_capex(),
            'opex_keuros_year1': self.opex_keuros_year1,
            'specific_opex_eur_kw_year': self.calculate_specific_opex(),
            'production_mwh_year1': self.production_mwh_year1,
            'capacity_factor': self.calculate_capacity_factor(),
            'ppa_price_eur_kwh': self.ppa_price_eur_kwh,
            'total_grants_meur': self.calculate_total_grants(),
            'grant_percentage': self.calculate_grant_percentage(),
            'debt_ratio': self.debt_ratio,
            'interest_rate': self.interest_rate,
            'discount_rate': self.discount_rate
        }
    
    def get_validation_status(self) -> Dict[str, Any]:
        """Get validation status for UI."""
        return {
            'is_valid': self.is_validated,
            'error_count': len(self.validation_errors),
            'errors': self.validation_errors,
            'warnings': self._get_warnings()
        }
    
    def _get_warnings(self) -> Dict[str, str]:
        """Get warnings for parameters that are valid but potentially concerning."""
        warnings = {}
        
        # Check for high debt ratio
        if self.debt_ratio > 0.8:
            warnings['debt_ratio'] = "High debt ratio may increase financial risk"
        
        # Check for low capacity factor
        capacity_factor = self.calculate_capacity_factor()
        if capacity_factor < 0.2:
            warnings['capacity_factor'] = "Low capacity factor may impact economics"
        
        # Check for high specific CAPEX
        specific_capex = self.calculate_specific_capex()
        if specific_capex > 1500:  # EUR/kW
            warnings['capex'] = "High specific CAPEX compared to industry benchmarks"
        
        # Check for low PPA price
        if self.ppa_price_eur_kwh < 0.03:
            warnings['ppa_price'] = "Low PPA price may impact project viability"
        
        return warnings
    
    def copy_with_modifications(self, **kwargs) -> 'EnhancedProjectAssumptions':
        """Create a copy with modifications for scenario analysis."""
        current_dict = self.to_dict()
        current_dict.update(kwargs)
        return EnhancedProjectAssumptions.from_dict(current_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        # Get base dictionary from parent class
        base_dict = super().to_dict() if hasattr(super(), 'to_dict') else {}
        
        # Add our fields
        base_dict.update({
            'is_validated': self.is_validated,
            'validation_errors': self.validation_errors,
            'last_modified': self.last_modified
        })
        
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedProjectAssumptions':
        """Create instance from dictionary."""
        # Handle case where data is already an EnhancedProjectAssumptions object
        if isinstance(data, cls):
            return data

        # Handle case where data is not a dictionary
        if not isinstance(data, dict):
            raise TypeError(f"Expected dict, got {type(data)}")

        # Filter out fields that don't belong to the dataclass
        valid_fields = {k: v for k, v in data.items() if k in cls.__dataclass_fields__}
        return cls(**valid_fields)
    
    def get_benchmark_comparison(self) -> Dict[str, Dict[str, Any]]:
        """Compare against industry benchmarks."""
        benchmarks = {
            'capacity_factor': {'value': 0.25, 'unit': '', 'description': 'Industry average'},
            'specific_capex': {'value': 1000, 'unit': 'EUR/kW', 'description': 'Industry average'},
            'specific_opex': {'value': 15, 'unit': 'EUR/kW/year', 'description': 'Industry average'},
            'debt_ratio': {'value': 0.75, 'unit': '', 'description': 'Typical project finance'},
            'interest_rate': {'value': 0.06, 'unit': '', 'description': 'Current market rates'},
            'project_irr_target': {'value': 0.12, 'unit': '', 'description': 'Minimum acceptable'}
        }
        
        current_values = {
            'capacity_factor': self.calculate_capacity_factor(),
            'specific_capex': self.calculate_specific_capex(),
            'specific_opex': self.calculate_specific_opex(),
            'debt_ratio': self.debt_ratio,
            'interest_rate': self.interest_rate,
            'project_irr_target': self.discount_rate  # Using discount rate as proxy
        }
        
        comparison = {}
        for metric, benchmark in benchmarks.items():
            current = current_values.get(metric, 0)
            comparison[metric] = {
                'current': current,
                'benchmark': benchmark['value'],
                'unit': benchmark['unit'],
                'description': benchmark['description'],
                'ratio': current / benchmark['value'] if benchmark['value'] != 0 else 0,
                'status': 'good' if abs(current - benchmark['value']) / benchmark['value'] < 0.2 else 'warning'
            }
        
        return comparison
