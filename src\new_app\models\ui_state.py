"""
UI State Management Models
==========================

Data models for managing UI state and navigation.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum


class TabState(Enum):
    """Enumeration of application tabs."""
    PROJECT_SETUP = "project_setup"
    DASHBOARD = "dashboard"
    LOCATION_COMPARISON = "location_comparison"
    FINANCIAL_MODEL = "financial_model"
    VALIDATION = "validation"
    SENSITIVITY = "sensitivity"
    MONTE_CARLO = "monte_carlo"
    SCENARIOS = "scenarios"
    EXPORT = "export"


@dataclass
class UIState:
    """Application UI state management."""
    
    # Navigation state
    current_tab: TabState = TabState.PROJECT_SETUP
    previous_tab: Optional[TabState] = None
    tab_history: List[TabState] = field(default_factory=list)
    
    # Loading states
    is_loading: bool = False
    loading_message: str = ""
    progress_value: float = 0.0
    progress_max: float = 100.0
    
    # Model execution state
    model_executed: bool = False
    model_execution_time: Optional[str] = None
    last_error: Optional[str] = None
    
    # Export state
    export_in_progress: bool = False
    export_type: Optional[str] = None
    export_progress: float = 0.0
    
    # Validation state
    validation_completed: bool = False
    validation_passed: bool = False
    validation_warnings: int = 0
    validation_errors: int = 0
    
    # Location comparison state
    selected_locations: List[str] = field(default_factory=lambda: ["Ouarzazate", "Dakhla"])
    comparison_completed: bool = False
    
    # Sensitivity analysis state
    sensitivity_variables: List[str] = field(default_factory=lambda: [
        'production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur', 'discount_rate'
    ])
    sensitivity_completed: bool = False
    
    # Monte Carlo state
    monte_carlo_simulations: int = 1000
    monte_carlo_completed: bool = False
    monte_carlo_progress: float = 0.0
    
    # Scenario analysis state
    selected_scenarios: List[str] = field(default_factory=lambda: ["Base", "Optimistic", "Pessimistic"])
    scenarios_completed: bool = False
    
    # UI preferences
    theme_mode: str = "light"
    show_tooltips: bool = True
    auto_save: bool = True
    chart_animation: bool = True
    
    def navigate_to_tab(self, tab: TabState):
        """Navigate to a specific tab."""
        if self.current_tab != tab:
            self.previous_tab = self.current_tab
            self.tab_history.append(self.current_tab)
            self.current_tab = tab
    
    def go_back(self) -> bool:
        """Go back to previous tab."""
        if self.previous_tab:
            current = self.current_tab
            self.current_tab = self.previous_tab
            self.previous_tab = current
            return True
        return False
    
    def set_loading(self, loading: bool, message: str = "", progress: float = 0.0):
        """Set loading state."""
        self.is_loading = loading
        self.loading_message = message
        self.progress_value = progress
    
    def update_progress(self, progress: float, message: str = ""):
        """Update progress."""
        self.progress_value = min(max(progress, 0.0), self.progress_max)
        if message:
            self.loading_message = message
    
    def set_error(self, error: Optional[str]):
        """Set error state."""
        self.last_error = error
        if error:
            self.is_loading = False
    
    def clear_error(self):
        """Clear error state."""
        self.last_error = None
    
    def set_model_executed(self, executed: bool, execution_time: Optional[str] = None):
        """Set model execution state."""
        self.model_executed = executed
        self.model_execution_time = execution_time
        if executed:
            self.clear_error()
    
    def set_validation_results(self, passed: bool, warnings: int = 0, errors: int = 0):
        """Set validation results."""
        self.validation_completed = True
        self.validation_passed = passed
        self.validation_warnings = warnings
        self.validation_errors = errors
    
    def set_export_progress(self, in_progress: bool, export_type: str = "", progress: float = 0.0):
        """Set export progress."""
        self.export_in_progress = in_progress
        self.export_type = export_type
        self.export_progress = progress
    
    def set_monte_carlo_progress(self, progress: float, completed: bool = False):
        """Set Monte Carlo progress."""
        self.monte_carlo_progress = progress
        self.monte_carlo_completed = completed
    
    def reset_analysis_states(self):
        """Reset all analysis completion states."""
        self.model_executed = False
        self.validation_completed = False
        self.comparison_completed = False
        self.sensitivity_completed = False
        self.monte_carlo_completed = False
        self.scenarios_completed = False
        self.clear_error()
    
    def get_status_summary(self) -> Dict[str, Any]:
        """Get status summary for display."""
        return {
            'current_tab': self.current_tab.value,
            'is_loading': self.is_loading,
            'model_executed': self.model_executed,
            'validation_passed': self.validation_passed,
            'has_error': self.last_error is not None,
            'export_in_progress': self.export_in_progress,
            'completion_status': {
                'model': self.model_executed,
                'validation': self.validation_completed,
                'comparison': self.comparison_completed,
                'sensitivity': self.sensitivity_completed,
                'monte_carlo': self.monte_carlo_completed,
                'scenarios': self.scenarios_completed
            }
        }
    
    def can_navigate_to_tab(self, tab: TabState) -> bool:
        """Check if navigation to tab is allowed based on current state."""
        # Project setup is always accessible
        if tab == TabState.PROJECT_SETUP:
            return True
        
        # Dashboard requires model execution
        if tab == TabState.DASHBOARD:
            return self.model_executed
        
        # Most analysis tabs require model execution
        analysis_tabs = [
            TabState.FINANCIAL_MODEL,
            TabState.VALIDATION,
            TabState.SENSITIVITY,
            TabState.MONTE_CARLO,
            TabState.SCENARIOS
        ]
        
        if tab in analysis_tabs:
            return self.model_executed
        
        # Location comparison is always accessible
        if tab == TabState.LOCATION_COMPARISON:
            return True
        
        # Export requires model execution
        if tab == TabState.EXPORT:
            return self.model_executed
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'current_tab': self.current_tab.value,
            'model_executed': self.model_executed,
            'validation_completed': self.validation_completed,
            'validation_passed': self.validation_passed,
            'selected_locations': self.selected_locations,
            'sensitivity_variables': self.sensitivity_variables,
            'monte_carlo_simulations': self.monte_carlo_simulations,
            'selected_scenarios': self.selected_scenarios,
            'theme_mode': self.theme_mode,
            'show_tooltips': self.show_tooltips,
            'auto_save': self.auto_save,
            'chart_animation': self.chart_animation
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UIState':
        """Create instance from dictionary."""
        instance = cls()
        
        # Set tab state
        if 'current_tab' in data:
            try:
                instance.current_tab = TabState(data['current_tab'])
            except ValueError:
                pass  # Keep default
        
        # Set other states
        for key in ['model_executed', 'validation_completed', 'validation_passed',
                   'selected_locations', 'sensitivity_variables', 'monte_carlo_simulations',
                   'selected_scenarios', 'theme_mode', 'show_tooltips', 'auto_save', 'chart_animation']:
            if key in data:
                setattr(instance, key, data[key])
        
        return instance
