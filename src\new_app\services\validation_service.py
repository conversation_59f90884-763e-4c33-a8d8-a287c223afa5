"""
Validation Service
==================

Service for model validation and benchmarking.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from typing import Dict, Any, List, Optional
import pandas as pd
import logging

from core.model_validation import validate_model_comprehensive, generate_benchmark_report, ModelValidator
from ..models.project_assumptions import EnhancedProjectAssumptions


class ValidationService:
    """Service for model validation and benchmarking."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._validation_results: Optional[Any] = None
        self._benchmark_results: Optional[Dict[str, Any]] = None
    
    def validate_model(self, 
                      assumptions: EnhancedProjectAssumptions,
                      kpis: Dict[str, Any],
                      cashflow: pd.DataFrame) -> Any:
        """Validate the financial model comprehensively."""
        try:
            self.logger.info("Starting comprehensive model validation")
            
            # Run comprehensive validation
            self._validation_results = validate_model_comprehensive(assumptions, kpis, cashflow)
            
            self.logger.info(f"Validation completed - Valid: {self._validation_results.is_valid}")
            return self._validation_results
            
        except Exception as e:
            self.logger.error(f"Error during model validation: {str(e)}")
            raise
    
    def generate_benchmark_comparison(self, 
                                    assumptions: EnhancedProjectAssumptions,
                                    kpis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate benchmark comparison report."""
        try:
            self.logger.info("Generating benchmark comparison")
            
            # Generate benchmark report
            self._benchmark_results = generate_benchmark_report(assumptions, kpis)
            
            # Add custom benchmark analysis
            custom_benchmarks = self._generate_custom_benchmarks(assumptions, kpis)
            self._benchmark_results.update(custom_benchmarks)
            
            self.logger.info("Benchmark comparison completed")
            return self._benchmark_results
            
        except Exception as e:
            self.logger.error(f"Error generating benchmark comparison: {str(e)}")
            raise
    
    def _generate_custom_benchmarks(self, 
                                  assumptions: EnhancedProjectAssumptions,
                                  kpis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate custom benchmark analysis."""
        benchmarks = {}
        
        # Industry benchmarks for solar PV projects
        industry_benchmarks = {
            'irr_project_min': 0.10,  # 10% minimum
            'irr_project_target': 0.12,  # 12% target
            'irr_equity_min': 0.15,  # 15% minimum
            'irr_equity_target': 0.18,  # 18% target
            'dscr_min': 1.20,  # Minimum DSCR
            'dscr_comfortable': 1.35,  # Comfortable DSCR
            'capacity_factor_min': 0.18,  # 18% minimum
            'capacity_factor_good': 0.25,  # 25% good
            'specific_capex_max': 1200,  # EUR/kW maximum
            'specific_capex_competitive': 1000,  # EUR/kW competitive
            'lcoe_competitive': 0.050,  # EUR/kWh competitive
            'grant_percentage_typical': 15.0  # 15% typical
        }
        
        # Calculate current metrics
        current_metrics = {
            'irr_project': kpis.get('IRR_project', 0),
            'irr_equity': kpis.get('IRR_equity', 0),
            'min_dscr': kpis.get('Min_DSCR', 0),
            'capacity_factor': assumptions.calculate_capacity_factor(),
            'specific_capex': assumptions.calculate_specific_capex(),
            'lcoe': kpis.get('LCOE_eur_kwh', 0),
            'grant_percentage': assumptions.calculate_grant_percentage()
        }
        
        # Compare against benchmarks
        benchmark_analysis = {}
        
        # IRR Analysis
        benchmark_analysis['irr_project'] = {
            'current': current_metrics['irr_project'],
            'benchmark_min': industry_benchmarks['irr_project_min'],
            'benchmark_target': industry_benchmarks['irr_project_target'],
            'status': self._get_irr_status(current_metrics['irr_project'], 
                                         industry_benchmarks['irr_project_min'],
                                         industry_benchmarks['irr_project_target']),
            'message': self._get_irr_message('project', current_metrics['irr_project'],
                                           industry_benchmarks['irr_project_min'],
                                           industry_benchmarks['irr_project_target'])
        }
        
        benchmark_analysis['irr_equity'] = {
            'current': current_metrics['irr_equity'],
            'benchmark_min': industry_benchmarks['irr_equity_min'],
            'benchmark_target': industry_benchmarks['irr_equity_target'],
            'status': self._get_irr_status(current_metrics['irr_equity'],
                                         industry_benchmarks['irr_equity_min'],
                                         industry_benchmarks['irr_equity_target']),
            'message': self._get_irr_message('equity', current_metrics['irr_equity'],
                                           industry_benchmarks['irr_equity_min'],
                                           industry_benchmarks['irr_equity_target'])
        }
        
        # DSCR Analysis
        benchmark_analysis['dscr'] = {
            'current': current_metrics['min_dscr'],
            'benchmark_min': industry_benchmarks['dscr_min'],
            'benchmark_comfortable': industry_benchmarks['dscr_comfortable'],
            'status': self._get_dscr_status(current_metrics['min_dscr'],
                                          industry_benchmarks['dscr_min'],
                                          industry_benchmarks['dscr_comfortable']),
            'message': self._get_dscr_message(current_metrics['min_dscr'],
                                            industry_benchmarks['dscr_min'],
                                            industry_benchmarks['dscr_comfortable'])
        }
        
        # Capacity Factor Analysis
        benchmark_analysis['capacity_factor'] = {
            'current': current_metrics['capacity_factor'],
            'benchmark_min': industry_benchmarks['capacity_factor_min'],
            'benchmark_good': industry_benchmarks['capacity_factor_good'],
            'status': self._get_capacity_factor_status(current_metrics['capacity_factor'],
                                                     industry_benchmarks['capacity_factor_min'],
                                                     industry_benchmarks['capacity_factor_good']),
            'message': self._get_capacity_factor_message(current_metrics['capacity_factor'],
                                                       industry_benchmarks['capacity_factor_min'],
                                                       industry_benchmarks['capacity_factor_good'])
        }
        
        # CAPEX Analysis
        benchmark_analysis['specific_capex'] = {
            'current': current_metrics['specific_capex'],
            'benchmark_competitive': industry_benchmarks['specific_capex_competitive'],
            'benchmark_max': industry_benchmarks['specific_capex_max'],
            'status': self._get_capex_status(current_metrics['specific_capex'],
                                           industry_benchmarks['specific_capex_competitive'],
                                           industry_benchmarks['specific_capex_max']),
            'message': self._get_capex_message(current_metrics['specific_capex'],
                                             industry_benchmarks['specific_capex_competitive'],
                                             industry_benchmarks['specific_capex_max'])
        }
        
        # LCOE Analysis
        benchmark_analysis['lcoe'] = {
            'current': current_metrics['lcoe'],
            'benchmark_competitive': industry_benchmarks['lcoe_competitive'],
            'status': 'good' if current_metrics['lcoe'] <= industry_benchmarks['lcoe_competitive'] else 'warning',
            'message': f"LCOE is {'competitive' if current_metrics['lcoe'] <= industry_benchmarks['lcoe_competitive'] else 'above market'}"
        }
        
        benchmarks['custom_analysis'] = benchmark_analysis
        benchmarks['industry_benchmarks'] = industry_benchmarks
        benchmarks['current_metrics'] = current_metrics
        
        return benchmarks
    
    def _get_irr_status(self, current: float, min_threshold: float, target: float) -> str:
        """Get IRR status based on benchmarks."""
        if current >= target:
            return 'excellent'
        elif current >= min_threshold:
            return 'good'
        else:
            return 'poor'
    
    def _get_irr_message(self, irr_type: str, current: float, min_threshold: float, target: float) -> str:
        """Get IRR message based on benchmarks."""
        if current >= target:
            return f"{irr_type.title()} IRR exceeds target ({current:.1%} vs {target:.1%})"
        elif current >= min_threshold:
            return f"{irr_type.title()} IRR meets minimum requirements ({current:.1%} vs {min_threshold:.1%})"
        else:
            return f"{irr_type.title()} IRR below minimum threshold ({current:.1%} vs {min_threshold:.1%})"
    
    def _get_dscr_status(self, current: float, min_threshold: float, comfortable: float) -> str:
        """Get DSCR status based on benchmarks."""
        if current >= comfortable:
            return 'excellent'
        elif current >= min_threshold:
            return 'acceptable'
        else:
            return 'poor'
    
    def _get_dscr_message(self, current: float, min_threshold: float, comfortable: float) -> str:
        """Get DSCR message based on benchmarks."""
        if current >= comfortable:
            return f"DSCR provides comfortable debt service coverage ({current:.2f})"
        elif current >= min_threshold:
            return f"DSCR meets minimum requirements ({current:.2f} vs {min_threshold:.2f})"
        else:
            return f"DSCR below minimum threshold - debt service risk ({current:.2f} vs {min_threshold:.2f})"
    
    def _get_capacity_factor_status(self, current: float, min_threshold: float, good: float) -> str:
        """Get capacity factor status."""
        if current >= good:
            return 'excellent'
        elif current >= min_threshold:
            return 'acceptable'
        else:
            return 'poor'
    
    def _get_capacity_factor_message(self, current: float, min_threshold: float, good: float) -> str:
        """Get capacity factor message."""
        if current >= good:
            return f"Excellent capacity factor ({current:.1%})"
        elif current >= min_threshold:
            return f"Acceptable capacity factor ({current:.1%})"
        else:
            return f"Low capacity factor may impact economics ({current:.1%})"
    
    def _get_capex_status(self, current: float, competitive: float, max_acceptable: float) -> str:
        """Get CAPEX status."""
        if current <= competitive:
            return 'excellent'
        elif current <= max_acceptable:
            return 'acceptable'
        else:
            return 'poor'
    
    def _get_capex_message(self, current: float, competitive: float, max_acceptable: float) -> str:
        """Get CAPEX message."""
        if current <= competitive:
            return f"Competitive specific CAPEX ({current:.0f} EUR/kW)"
        elif current <= max_acceptable:
            return f"Acceptable specific CAPEX ({current:.0f} EUR/kW)"
        else:
            return f"High specific CAPEX may impact competitiveness ({current:.0f} EUR/kW)"
    
    def get_validation_results(self) -> Optional[Any]:
        """Get current validation results."""
        return self._validation_results
    
    def get_benchmark_results(self) -> Optional[Dict[str, Any]]:
        """Get current benchmark results."""
        return self._benchmark_results
    
    def has_validation_results(self) -> bool:
        """Check if validation has been performed."""
        return self._validation_results is not None
    
    def clear_results(self):
        """Clear all validation results."""
        self._validation_results = None
        self._benchmark_results = None
        self.logger.info("Validation results cleared")
