"""
Test Utilities
==============

Unit tests for utility functions.
"""

import unittest
from unittest.mock import patch, mock_open, MagicMock
from pathlib import Path
import json
from datetime import datetime

from ..utils.file_utils import FileUtils
from ..utils.validation_utils import ValidationUtils
from ..utils.formatting_utils import FormattingUtils
from ..models.client_profile import ClientProfile


class TestFileUtils(unittest.TestCase):
    """Test cases for FileUtils."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.file_utils = FileUtils()
        self.client_profile = ClientProfile()
        self.client_profile.company_name = "Test Company"
        self.client_profile.client_name = "<PERSON>"
    
    @patch('pathlib.Path.mkdir')
    def test_create_timestamped_output_directory(self, mock_mkdir):
        """Test timestamped directory creation."""
        directories = self.file_utils.create_timestamped_output_directory(self.client_profile)
        
        self.assertIsInstance(directories, dict)
        self.assertIn('main_dir', directories)
        self.assertIn('data_dir', directories)
        self.assertIn('charts_dir', directories)
        self.assertIn('reports_dir', directories)
        
        # Check that mkdir was called
        self.assertTrue(mock_mkdir.called)
    
    def test_generate_filename(self):
        """Test filename generation."""
        filename = self.file_utils.generate_filename('excel', self.client_profile)
        
        self.assertIsInstance(filename, str)
        self.assertTrue(filename.endswith('.xlsx'))
        self.assertIn('Test_Company', filename)
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('json.dump')
    @patch('pathlib.Path.mkdir')
    def test_save_json_data(self, mock_mkdir, mock_json_dump, mock_file):
        """Test JSON data saving."""
        test_data = {'key': 'value', 'number': 123}
        test_path = Path('/test/path/data.json')
        
        result = self.file_utils.save_json_data(test_data, test_path)
        
        self.assertTrue(result)
        mock_file.assert_called_once()
        mock_json_dump.assert_called_once_with(
            test_data, mock_file.return_value.__enter__.return_value,
            indent=2, ensure_ascii=False, default=str
        )
    
    @patch('builtins.open', new_callable=mock_open, read_data='{"key": "value"}')
    @patch('pathlib.Path.exists')
    def test_load_json_data(self, mock_exists, mock_file):
        """Test JSON data loading."""
        mock_exists.return_value = True
        test_path = Path('/test/path/data.json')
        
        with patch('json.load') as mock_json_load:
            mock_json_load.return_value = {'key': 'value'}
            
            result = self.file_utils.load_json_data(test_path)
            
            self.assertEqual(result, {'key': 'value'})
            mock_file.assert_called_once()
            mock_json_load.assert_called_once()
    
    def test_get_safe_filename(self):
        """Test safe filename generation."""
        unsafe_filename = 'Test<>File|Name?.txt'
        safe_filename = self.file_utils.get_safe_filename(unsafe_filename)
        
        self.assertEqual(safe_filename, 'Test_File_Name_.txt')
        
        # Test with multiple underscores
        unsafe_filename2 = 'Test___File___Name.txt'
        safe_filename2 = self.file_utils.get_safe_filename(unsafe_filename2)
        
        self.assertEqual(safe_filename2, 'Test_File_Name.txt')
    
    def test_format_file_size(self):
        """Test file size formatting."""
        self.assertEqual(self.file_utils.format_file_size(0), "0 B")
        self.assertEqual(self.file_utils.format_file_size(1024), "1.0 KB")
        self.assertEqual(self.file_utils.format_file_size(1048576), "1.0 MB")
        self.assertEqual(self.file_utils.format_file_size(1073741824), "1.0 GB")


class TestValidationUtils(unittest.TestCase):
    """Test cases for ValidationUtils."""
    
    def test_validate_email(self):
        """Test email validation."""
        # Valid emails
        self.assertTrue(ValidationUtils.validate_email("<EMAIL>"))
        self.assertTrue(ValidationUtils.validate_email("<EMAIL>"))
        self.assertTrue(ValidationUtils.validate_email(""))  # Empty is valid
        
        # Invalid emails
        self.assertFalse(ValidationUtils.validate_email("invalid-email"))
        self.assertFalse(ValidationUtils.validate_email("@domain.com"))
        self.assertFalse(ValidationUtils.validate_email("user@"))
    
    def test_validate_phone(self):
        """Test phone validation."""
        # Valid phones
        self.assertTrue(ValidationUtils.validate_phone("1234567890"))
        self.assertTrue(ValidationUtils.validate_phone("+****************"))
        self.assertTrue(ValidationUtils.validate_phone(""))  # Empty is valid
        
        # Invalid phones
        self.assertFalse(ValidationUtils.validate_phone("123"))  # Too short
        self.assertFalse(ValidationUtils.validate_phone("abc123def"))  # Contains letters
    
    def test_validate_positive_number(self):
        """Test positive number validation."""
        # Valid positive numbers
        self.assertTrue(ValidationUtils.validate_positive_number(5.0))
        self.assertTrue(ValidationUtils.validate_positive_number(100))
        self.assertTrue(ValidationUtils.validate_positive_number(0.1))
        
        # Test with allow_zero
        self.assertFalse(ValidationUtils.validate_positive_number(0))
        self.assertTrue(ValidationUtils.validate_positive_number(0, allow_zero=True))
        
        # Invalid numbers
        self.assertFalse(ValidationUtils.validate_positive_number(-5.0))
        self.assertFalse(ValidationUtils.validate_positive_number(None))
    
    def test_validate_percentage(self):
        """Test percentage validation."""
        # Valid percentages (0-1 range)
        self.assertTrue(ValidationUtils.validate_percentage(0.5))
        self.assertTrue(ValidationUtils.validate_percentage(0.0))
        self.assertTrue(ValidationUtils.validate_percentage(1.0))
        
        # Invalid percentages
        self.assertFalse(ValidationUtils.validate_percentage(-0.1))
        self.assertFalse(ValidationUtils.validate_percentage(1.5))
        self.assertFalse(ValidationUtils.validate_percentage(None))
    
    def test_validate_capacity_factor(self):
        """Test capacity factor validation."""
        # Valid capacity factor
        result = ValidationUtils.validate_capacity_factor(18000, 10.0)
        self.assertTrue(result['is_valid'])
        self.assertAlmostEqual(result['capacity_factor'], 18000 / (10.0 * 8760), places=4)
        
        # High capacity factor (should generate warning)
        result = ValidationUtils.validate_capacity_factor(30000, 10.0)
        self.assertTrue(result['is_valid'])
        self.assertGreater(len(result['warnings']), 0)
        
        # Invalid inputs
        result = ValidationUtils.validate_capacity_factor(-1000, 10.0)
        self.assertFalse(result['is_valid'])
        self.assertGreater(len(result['errors']), 0)
    
    def test_validate_financial_ratios(self):
        """Test financial ratios validation."""
        # Valid ratios
        result = ValidationUtils.validate_financial_ratios(0.75, 0.06, 0.08)
        self.assertTrue(result['is_valid'])
        
        # Invalid debt ratio
        result = ValidationUtils.validate_financial_ratios(1.2, 0.06, 0.08)
        self.assertFalse(result['is_valid'])
        self.assertGreater(len(result['errors']), 0)
        
        # Discount rate lower than interest rate (should warn)
        result = ValidationUtils.validate_financial_ratios(0.75, 0.08, 0.06)
        self.assertTrue(result['is_valid'])
        self.assertGreater(len(result['warnings']), 0)


class TestFormattingUtils(unittest.TestCase):
    """Test cases for FormattingUtils."""
    
    def test_format_currency(self):
        """Test currency formatting."""
        # Test scaling
        self.assertEqual(FormattingUtils.format_currency(1000000), "1.0M EUR")
        self.assertEqual(FormattingUtils.format_currency(1500000), "1.5M EUR")
        self.assertEqual(FormattingUtils.format_currency(1000), "1.0k EUR")
        self.assertEqual(FormattingUtils.format_currency(500), "500.00 EUR")
        
        # Test different currencies
        self.assertEqual(FormattingUtils.format_currency(1000, "USD"), "1.0k USD")
        
        # Test negative values
        self.assertEqual(FormattingUtils.format_currency(-1000000), "-1.0M EUR")
        
        # Test None
        self.assertEqual(FormattingUtils.format_currency(None), "N/A")
    
    def test_format_percentage(self):
        """Test percentage formatting."""
        self.assertEqual(FormattingUtils.format_percentage(0.15), "15.0%")
        self.assertEqual(FormattingUtils.format_percentage(0.1234, precision=2), "12.34%")
        self.assertEqual(FormattingUtils.format_percentage(15.0, multiply_by_100=False), "15.0%")
        self.assertEqual(FormattingUtils.format_percentage(None), "N/A")
    
    def test_format_number(self):
        """Test number formatting."""
        self.assertEqual(FormattingUtils.format_number(1234567.89), "1,234,567.89")
        self.assertEqual(FormattingUtils.format_number(1234567.89, precision=0), "1,234,568")
        self.assertEqual(FormattingUtils.format_number(1234567.89, use_thousands_separator=False), "1234567.89")
        self.assertEqual(FormattingUtils.format_number(None), "N/A")
    
    def test_format_large_number(self):
        """Test large number formatting."""
        self.assertEqual(FormattingUtils.format_large_number(1500000000), "1.5B")
        self.assertEqual(FormattingUtils.format_large_number(1500000), "1.5M")
        self.assertEqual(FormattingUtils.format_large_number(1500), "1.5k")
        self.assertEqual(FormattingUtils.format_large_number(150), "150.0")
        self.assertEqual(FormattingUtils.format_large_number(None), "N/A")
    
    def test_format_energy(self):
        """Test energy formatting."""
        self.assertEqual(FormattingUtils.format_energy(18000, "MWh"), "18.0 GWh")
        self.assertEqual(FormattingUtils.format_energy(1500, "MWh"), "1.5 GWh")
        self.assertEqual(FormattingUtils.format_energy(500, "MWh"), "500.0 MWh")
        self.assertEqual(FormattingUtils.format_energy(None), "N/A")
    
    def test_format_power(self):
        """Test power formatting."""
        self.assertEqual(FormattingUtils.format_power(10, "MW"), "10.0 MW")
        self.assertEqual(FormattingUtils.format_power(1500, "MW"), "1.5 GW")
        self.assertEqual(FormattingUtils.format_power(500, "kW"), "500.0 kW")
        self.assertEqual(FormattingUtils.format_power(None), "N/A")
    
    def test_format_ratio(self):
        """Test ratio formatting."""
        self.assertEqual(FormattingUtils.format_ratio(3, 4), "0.75")
        self.assertEqual(FormattingUtils.format_ratio(5, 0), "∞")
        self.assertEqual(FormattingUtils.format_ratio(0, 0), "N/A")
        self.assertEqual(FormattingUtils.format_ratio(None, 5), "N/A")
    
    def test_truncate_text(self):
        """Test text truncation."""
        long_text = "This is a very long text that should be truncated"
        truncated = FormattingUtils.truncate_text(long_text, 20)
        self.assertEqual(truncated, "This is a very lo...")
        self.assertEqual(len(truncated), 20)
        
        short_text = "Short text"
        not_truncated = FormattingUtils.truncate_text(short_text, 20)
        self.assertEqual(not_truncated, "Short text")


if __name__ == '__main__':
    unittest.main()
