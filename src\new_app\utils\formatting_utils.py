"""
Formatting Utilities
====================

Utility functions for data formatting and display.
"""

from typing import Union, Optional
from datetime import datetime
import locale


class FormattingUtils:
    """Utility class for data formatting."""
    
    @staticmethod
    def format_currency(value: Union[int, float], 
                       currency: str = "EUR", 
                       precision: int = 2,
                       use_thousands_separator: bool = True) -> str:
        """Format currency value with appropriate scaling."""
        if value is None:
            return "N/A"
        
        try:
            abs_value = abs(value)
            sign = "-" if value < 0 else ""
            
            # Determine scaling
            if abs_value >= 1e9:
                scaled_value = value / 1e9
                suffix = "B"
                precision = 1
            elif abs_value >= 1e6:
                scaled_value = value / 1e6
                suffix = "M"
                precision = 1
            elif abs_value >= 1e3:
                scaled_value = value / 1e3
                suffix = "k"
                precision = 1
            else:
                scaled_value = value
                suffix = ""
            
            # Format number
            if use_thousands_separator:
                formatted_value = f"{scaled_value:,.{precision}f}"
            else:
                formatted_value = f"{scaled_value:.{precision}f}"
            
            return f"{sign}{formatted_value}{suffix} {currency}"
            
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_percentage(value: Union[int, float], 
                         precision: int = 1,
                         multiply_by_100: bool = True) -> str:
        """Format percentage value."""
        if value is None:
            return "N/A"
        
        try:
            if multiply_by_100:
                percentage_value = value * 100
            else:
                percentage_value = value
            
            return f"{percentage_value:.{precision}f}%"
            
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_number(value: Union[int, float], 
                     precision: int = 2,
                     use_thousands_separator: bool = True) -> str:
        """Format number with thousands separator."""
        if value is None:
            return "N/A"
        
        try:
            if use_thousands_separator:
                return f"{value:,.{precision}f}"
            else:
                return f"{value:.{precision}f}"
                
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_large_number(value: Union[int, float], 
                           precision: int = 1) -> str:
        """Format large numbers with appropriate scaling."""
        if value is None:
            return "N/A"
        
        try:
            abs_value = abs(value)
            sign = "-" if value < 0 else ""
            
            if abs_value >= 1e12:
                return f"{sign}{value/1e12:.{precision}f}T"
            elif abs_value >= 1e9:
                return f"{sign}{value/1e9:.{precision}f}B"
            elif abs_value >= 1e6:
                return f"{sign}{value/1e6:.{precision}f}M"
            elif abs_value >= 1e3:
                return f"{sign}{value/1e3:.{precision}f}k"
            else:
                return f"{sign}{value:.{precision}f}"
                
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_energy(value: Union[int, float], 
                     unit: str = "MWh",
                     precision: int = 1) -> str:
        """Format energy values with appropriate units."""
        if value is None:
            return "N/A"
        
        try:
            abs_value = abs(value)
            sign = "-" if value < 0 else ""
            
            # Convert to base unit (Wh)
            unit_multipliers = {
                "Wh": 1,
                "kWh": 1e3,
                "MWh": 1e6,
                "GWh": 1e9,
                "TWh": 1e12
            }
            
            base_value = value * unit_multipliers.get(unit, 1)
            
            # Determine best unit
            if abs(base_value) >= 1e12:
                return f"{sign}{base_value/1e12:.{precision}f} TWh"
            elif abs(base_value) >= 1e9:
                return f"{sign}{base_value/1e9:.{precision}f} GWh"
            elif abs(base_value) >= 1e6:
                return f"{sign}{base_value/1e6:.{precision}f} MWh"
            elif abs(base_value) >= 1e3:
                return f"{sign}{base_value/1e3:.{precision}f} kWh"
            else:
                return f"{sign}{base_value:.{precision}f} Wh"
                
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_power(value: Union[int, float], 
                    unit: str = "MW",
                    precision: int = 1) -> str:
        """Format power values with appropriate units."""
        if value is None:
            return "N/A"
        
        try:
            abs_value = abs(value)
            sign = "-" if value < 0 else ""
            
            # Convert to base unit (W)
            unit_multipliers = {
                "W": 1,
                "kW": 1e3,
                "MW": 1e6,
                "GW": 1e9
            }
            
            base_value = value * unit_multipliers.get(unit, 1)
            
            # Determine best unit
            if abs(base_value) >= 1e9:
                return f"{sign}{base_value/1e9:.{precision}f} GW"
            elif abs(base_value) >= 1e6:
                return f"{sign}{base_value/1e6:.{precision}f} MW"
            elif abs(base_value) >= 1e3:
                return f"{sign}{base_value/1e3:.{precision}f} kW"
            else:
                return f"{sign}{base_value:.{precision}f} W"
                
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_datetime(dt: datetime, 
                       format_string: str = "%Y-%m-%d %H:%M:%S") -> str:
        """Format datetime object."""
        if dt is None:
            return "N/A"
        
        try:
            return dt.strftime(format_string)
        except (ValueError, AttributeError):
            return "Invalid"
    
    @staticmethod
    def format_date(dt: datetime, 
                   format_string: str = "%Y-%m-%d") -> str:
        """Format date only."""
        return FormattingUtils.format_datetime(dt, format_string)
    
    @staticmethod
    def format_time(dt: datetime, 
                   format_string: str = "%H:%M:%S") -> str:
        """Format time only."""
        return FormattingUtils.format_datetime(dt, format_string)
    
    @staticmethod
    def format_duration(seconds: Union[int, float]) -> str:
        """Format duration in seconds to human readable format."""
        if seconds is None:
            return "N/A"
        
        try:
            seconds = int(seconds)
            
            if seconds < 60:
                return f"{seconds}s"
            elif seconds < 3600:
                minutes = seconds // 60
                remaining_seconds = seconds % 60
                return f"{minutes}m {remaining_seconds}s"
            elif seconds < 86400:
                hours = seconds // 3600
                remaining_minutes = (seconds % 3600) // 60
                return f"{hours}h {remaining_minutes}m"
            else:
                days = seconds // 86400
                remaining_hours = (seconds % 86400) // 3600
                return f"{days}d {remaining_hours}h"
                
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_ratio(numerator: Union[int, float], 
                    denominator: Union[int, float],
                    precision: int = 2) -> str:
        """Format ratio with proper handling of division by zero."""
        if numerator is None or denominator is None:
            return "N/A"
        
        try:
            if denominator == 0:
                return "∞" if numerator > 0 else "N/A"
            
            ratio = numerator / denominator
            return f"{ratio:.{precision}f}"
            
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes is None:
            return "N/A"
        
        try:
            if size_bytes == 0:
                return "0 B"
            
            size_names = ["B", "KB", "MB", "GB", "TB"]
            i = 0
            size = float(size_bytes)
            
            while size >= 1024 and i < len(size_names) - 1:
                size /= 1024.0
                i += 1
            
            return f"{size:.1f} {size_names[i]}"
            
        except (ValueError, TypeError):
            return "Invalid"
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
        """Truncate text to specified length."""
        if not text:
            return ""
        
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
