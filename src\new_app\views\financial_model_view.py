"""
Financial Model View
====================

View component for detailed financial model results.
"""

import flet as ft
from typing import Dict, Any, Optional

from .base_view import BaseView


class FinancialModelView(BaseView):
    """View for detailed financial model results."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.financial_results: Optional[Dict[str, Any]] = None
    
    def build_content(self) -> ft.Control:
        """Build the financial model view content."""
        
        if not self.financial_results:
            return self.create_empty_state(
                "No Financial Results",
                "Run the financial model first to see detailed results",
                "Go to Project Setup",
                lambda: self.navigate_to("project_setup")
            )
        
        # Header
        header = self.create_section_header(
            "Financial Model Results",
            "Detailed financial analysis and cashflow projections"
        )
        
        # KPI Summary
        kpi_summary = self._create_kpi_summary()
        
        # Cashflow Table
        cashflow_table = self._create_cashflow_table()
        
        # Additional Metrics
        additional_metrics = self._create_additional_metrics()
        
        return ft.Column([
            header,
            kpi_summary,
            cashflow_table,
            additional_metrics
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_kpi_summary(self) -> ft.Card:
        """Create KPI summary section."""
        kpis = self.financial_results.get('kpis', {})
        
        kpi_data = [
            ("Project IRR", f"{kpis.get('IRR_project', 0):.1%}"),
            ("Equity IRR", f"{kpis.get('IRR_equity', 0):.1%}"),
            ("NPV Project", self.format_currency(kpis.get('NPV_project', 0))),
            ("NPV Equity", self.format_currency(kpis.get('NPV_equity', 0))),
            ("LCOE", f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh"),
            ("Min DSCR", f"{kpis.get('Min_DSCR', 0):.2f}"),
            ("Avg DSCR", f"{kpis.get('Avg_DSCR', 0):.2f}"),
            ("Payback Period", f"{kpis.get('Payback_years', 0):.1f} years")
        ]
        
        kpi_rows = [self.create_info_row(label, value) for label, value in kpi_data]
        
        return self.create_card(
            "Key Performance Indicators",
            ft.Column(kpi_rows),
            icon=ft.Icons.ANALYTICS
        )
    
    def _create_cashflow_table(self) -> ft.Card:
        """Create cashflow table (placeholder)."""
        # This would contain a detailed cashflow table
        placeholder = ft.Container(
            content=ft.Text("Detailed cashflow table would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
        
        return self.create_card(
            "Cashflow Projections",
            placeholder,
            icon=ft.Icons.TABLE_VIEW
        )
    
    def _create_additional_metrics(self) -> ft.Card:
        """Create additional metrics section."""
        # Placeholder for additional financial metrics
        placeholder = ft.Container(
            content=ft.Text("Additional financial metrics would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=200,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
        
        return self.create_card(
            "Additional Metrics",
            placeholder,
            icon=ft.Icons.CALCULATE
        )
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with new financial results."""
        if "financial_results" in data:
            self.financial_results = data["financial_results"]
            self.refresh()
    
    def set_financial_results(self, results: Dict[str, Any]):
        """Set financial results."""
        self.financial_results = results
        self.refresh()
