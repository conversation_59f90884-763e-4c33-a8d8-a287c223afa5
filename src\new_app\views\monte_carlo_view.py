"""
Monte Carlo Simulation View
===========================

View component for Monte Carlo simulation results.
"""

import flet as ft
from typing import Dict, Any, Optional

from .base_view import BaseView


class MonteCarloView(BaseView):
    """View for Monte Carlo simulation results."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.monte_carlo_results: Optional[Dict[str, Any]] = None
        self.n_simulations = 1000
    
    def build_content(self) -> ft.Control:
        """Build the Monte Carlo view content."""
        
        # Header
        header = self.create_section_header(
            "Monte Carlo Simulation",
            "Risk analysis through probabilistic modeling"
        )
        
        # Simulation settings
        simulation_settings = self._create_simulation_settings()
        
        # Results display
        if self.monte_carlo_results:
            results_content = self._create_results_display()
        else:
            results_content = self.create_empty_state(
                "No Monte Carlo Results",
                "Run Monte Carlo simulation to see risk analysis",
                "Run Simulation",
                self._on_run_simulation
            )
        
        return ft.Column([
            header,
            simulation_settings,
            results_content
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_simulation_settings(self) -> ft.Card:
        """Create simulation settings interface."""
        settings_content = ft.Column([
            ft.Text("Simulation Settings:", size=16, weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.Text("Number of Simulations:", expand=1),
                ft.TextField(
                    value=str(self.n_simulations),
                    width=150,
                    on_change=self._on_simulations_changed
                )
            ]),
            ft.Divider(height=20),
            self.create_action_button(
                "Run Monte Carlo Simulation",
                ft.Icons.CASINO,
                self._on_run_simulation,
                ft.Colors.PURPLE_600
            )
        ])
        
        return self.create_card(
            "Simulation Configuration",
            settings_content,
            icon=ft.Icons.SETTINGS
        )
    
    def _create_results_display(self) -> ft.Card:
        """Create Monte Carlo results display."""
        # Placeholder for Monte Carlo results
        placeholder = ft.Container(
            content=ft.Text("Monte Carlo simulation results and risk charts would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=400,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
        
        return self.create_card(
            "Monte Carlo Results",
            placeholder,
            icon=ft.Icons.ASSESSMENT
        )
    
    def _on_simulations_changed(self, e):
        """Handle number of simulations change."""
        try:
            self.n_simulations = int(e.control.value)
        except ValueError:
            self.n_simulations = 1000
    
    def _on_run_simulation(self, e=None):
        """Run Monte Carlo simulation."""
        self.request_action("run_monte_carlo", {
            "n_simulations": self.n_simulations
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with Monte Carlo results."""
        if "monte_carlo_results" in data:
            self.monte_carlo_results = data["monte_carlo_results"]
            self.refresh()
    
    def set_monte_carlo_results(self, results: Dict[str, Any]):
        """Set Monte Carlo results."""
        self.monte_carlo_results = results
        self.refresh()
