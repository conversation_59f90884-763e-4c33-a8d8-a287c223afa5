"""
Validation View
===============

View component for model validation and benchmarking.
"""

import flet as ft
from typing import Dict, Any, Optional

from .base_view import BaseView


class ValidationView(BaseView):
    """View for model validation and benchmarking."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.validation_results: Optional[Any] = None
        self.benchmark_results: Optional[Dict[str, Any]] = None
    
    def build_content(self) -> ft.Control:
        """Build the validation view content."""
        
        if not self.validation_results:
            return self.create_empty_state(
                "No Validation Results",
                "Run the financial model first to see validation results",
                "Go to Project Setup",
                lambda: self.navigate_to("project_setup")
            )
        
        # Header
        header = self.create_section_header(
            "Model Validation & Benchmarks",
            "Comprehensive model validation and industry benchmarking"
        )
        
        # Validation Status
        validation_status = self._create_validation_status()
        
        # Validation Issues
        validation_issues = self._create_validation_issues()
        
        # Benchmark Comparison
        benchmark_comparison = self._create_benchmark_comparison()
        
        return ft.Column([
            header,
            validation_status,
            validation_issues,
            benchmark_comparison
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_validation_status(self) -> ft.Card:
        """Create validation status display."""
        is_valid = getattr(self.validation_results, 'is_valid', False)
        
        status_color = ft.Colors.GREEN if is_valid else ft.Colors.RED
        status_icon = ft.Icons.CHECK_CIRCLE if is_valid else ft.Icons.ERROR
        status_text = "MODEL VALIDATION PASSED" if is_valid else "MODEL VALIDATION FAILED"
        
        status_content = ft.Row([
            ft.Icon(status_icon, color=status_color, size=40),
            ft.Text(status_text, size=20, weight=ft.FontWeight.BOLD, color=status_color)
        ], alignment=ft.MainAxisAlignment.CENTER)
        
        return self.create_card(
            "Validation Status",
            status_content,
            bgcolor=f"{status_color}10"
        )
    
    def _create_validation_issues(self) -> ft.Card:
        """Create validation issues display."""
        issues_content = ft.Column([])
        
        # Warnings
        warnings = getattr(self.validation_results, 'warnings', [])
        if warnings:
            issues_content.controls.append(
                ft.ExpansionTile(
                    title=ft.Text(f"Warnings ({len(warnings)})", color=ft.Colors.ORANGE),
                    leading=ft.Icon(ft.Icons.WARNING, color=ft.Colors.ORANGE),
                    controls=[
                        ft.ListTile(title=ft.Text(warning, color=ft.Colors.ORANGE_700))
                        for warning in warnings
                    ]
                )
            )
        
        # Errors
        errors = getattr(self.validation_results, 'errors', [])
        if errors:
            issues_content.controls.append(
                ft.ExpansionTile(
                    title=ft.Text(f"Errors ({len(errors)})", color=ft.Colors.RED),
                    leading=ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED),
                    controls=[
                        ft.ListTile(title=ft.Text(error, color=ft.Colors.RED_700))
                        for error in errors
                    ]
                )
            )
        
        # Recommendations
        recommendations = getattr(self.validation_results, 'recommendations', [])
        if recommendations:
            issues_content.controls.append(
                ft.ExpansionTile(
                    title=ft.Text(f"Recommendations ({len(recommendations)})", color=ft.Colors.BLUE),
                    leading=ft.Icon(ft.Icons.LIGHTBULB, color=ft.Colors.BLUE),
                    controls=[
                        ft.ListTile(title=ft.Text(rec, color=ft.Colors.BLUE_700))
                        for rec in recommendations
                    ]
                )
            )
        
        if not issues_content.controls:
            issues_content.controls.append(
                ft.Text("No validation issues found", 
                       color=ft.Colors.GREEN, 
                       text_align=ft.TextAlign.CENTER)
            )
        
        return self.create_card(
            "Validation Results",
            issues_content,
            icon=ft.Icons.CHECKLIST
        )
    
    def _create_benchmark_comparison(self) -> ft.Card:
        """Create benchmark comparison display."""
        if not self.benchmark_results:
            placeholder = ft.Container(
                content=ft.Text("Benchmark comparison would be displayed here",
                              text_align=ft.TextAlign.CENTER),
                height=300,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.GREY_100,
                border_radius=8
            )
            
            return self.create_card(
                "Industry Benchmarks",
                placeholder,
                icon=ft.Icons.COMPARE
            )
        
        # Display benchmark results
        benchmark_content = ft.Column([
            ft.Text("Industry Benchmark Comparison", size=16, weight=ft.FontWeight.BOLD),
            ft.Text("Detailed benchmark analysis would be displayed here", 
                   color=ft.Colors.GREY_600)
        ])
        
        return self.create_card(
            "Industry Benchmarks",
            benchmark_content,
            icon=ft.Icons.COMPARE
        )
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with validation results."""
        if "validation_results" in data:
            self.validation_results = data["validation_results"]
            self.refresh()
        
        if "benchmark_results" in data:
            self.benchmark_results = data["benchmark_results"]
            self.refresh()
    
    def set_validation_results(self, results: Any):
        """Set validation results."""
        self.validation_results = results
        self.refresh()
    
    def set_benchmark_results(self, results: Dict[str, Any]):
        """Set benchmark results."""
        self.benchmark_results = results
        self.refresh()
